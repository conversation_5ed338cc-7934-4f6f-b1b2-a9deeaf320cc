<template>
  <div class="photo-note-detail">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="照片笔记"
      left-arrow
      @click-left="$router.back()"
      class="detail-navbar"
    >
      <template #right>
        <van-icon name="share-o" @click="shareNote" />
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <van-loading v-if="loading" type="spinner" color="#1989fa" vertical class="loading-container">
      加载中...
    </van-loading>

    <!-- 照片笔记内容 -->
    <div v-if="!loading && noteDetail" class="note-content">
      <!-- 用户信息 -->
      <div class="user-info">
        <van-image
          :src="noteDetail.avatar"
          round
          width="40"
          height="40"
          fit="cover"
          class="user-avatar"
          @click="goToUserProfile"
        />
        <div class="user-details">
          <div class="user-name" @click="goToUserProfile">{{ noteDetail.nickname }}</div>
          <div class="publish-time">{{ formatTime(noteDetail.createdAt) }}</div>
        </div>
      </div>

      <!-- 照片展示 -->
      <div class="photo-section">
        <div class="photo-grid" :class="getPhotoGridClass">
          <div
            v-for="(image, index) in noteDetail.images"
            :key="image.photoId"
            class="photo-item"
            @click="previewPhoto(index)"
          >
            <van-image
              :src="privateImageUrls[index] || image.thumbnailUrl || image.url"
              fit="cover"
              width="100%"
              height="100%"
              :alt="`照片${index + 1}`"
            >
              <template #loading>
                <van-loading type="spinner" size="20" />
              </template>
              <template #error>
                <div class="error-placeholder">
                  <van-icon name="photo-fail" size="24" />
                </div>
              </template>
            </van-image>
          </div>
        </div>
      </div>

      <!-- 标题和内容 -->
      <div class="content-section">
        <h2 v-if="noteDetail.title" class="note-title">{{ noteDetail.title }}</h2>
        <div class="note-content-text" v-html="noteDetail.processedContent || noteDetail.content"></div>
      </div>
    </div>

    <!-- 错误状态 -->
    <van-empty v-else-if="!loading && !noteDetail" description="照片笔记不存在或已被删除" />

    <!-- 照片预览 -->
    <van-image-preview
      v-model="showPreview"
      :images="previewImages"
      :start-position="previewIndex"
      @change="onPreviewChange"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showShareSheet } from 'vant'
import { getPhotoNoteDetail, likePhotoNote, unlikePhotoNote, collectPhotoNote, uncollectPhotoNote } from '@/api/photo'
import { getPrivateImageUrl } from '@/api/file'
import { formatRelativeTime } from '@/utils/time'

const route = useRoute()
const router = useRouter()

// 响应式数据
const noteDetail = ref(null)
const loading = ref(true)
const showPreview = ref(false)
const previewIndex = ref(0)
const privateImageUrls = ref([])

// 计算属性
const previewImages = computed(() => {
  if (!noteDetail.value || !noteDetail.value.images) return []
  return noteDetail.value.images.map((image, index) =>
    privateImageUrls.value[index] || image.url
  )
})

const getPhotoGridClass = computed(() => {
  const count = noteDetail.value && noteDetail.value.images ? noteDetail.value.images.length : 0
  if (count === 1) return 'grid-1'
  if (count <= 4) return 'grid-2x2'
  return 'grid-3x3'
})

// 方法
const loadNoteDetail = async () => {
  try {
    loading.value = true
    const noteId = route.params.id
    console.log('开始加载照片笔记详情，ID:', noteId)

    const response = await getPhotoNoteDetail(Number(noteId))
    console.log('照片笔记详情API响应:', response)

    // 处理不同的响应格式
    let data = null
    if (response && response.data) {
      data = response.data
    } else if (response && response.id) {
      data = response
    } else {
      console.error('API响应数据格式错误:', response)
      showToast('数据格式错误')
      return
    }

    noteDetail.value = data
    console.log('设置noteDetail.value:', noteDetail.value)

    // 处理私有图片URL
    if (noteDetail.value && noteDetail.value.images && noteDetail.value.images.length > 0) {
      console.log('开始处理私有图片URL，图片数量:', noteDetail.value.images.length)
      const urls = await Promise.all(
        noteDetail.value.images.map(async (image, index) => {
          try {
            console.log('处理第' + (index + 1) + '张图片:', image)
            const originalUrl = image.thumbnailUrl || image.url
            console.log('原始URL:', originalUrl)
            const processedUrl = await getPrivateImageUrl(originalUrl)
            console.log('处理后URL:', processedUrl)
            return processedUrl
          } catch (error) {
            console.error('获取私有图片URL失败:', error)
            return image.thumbnailUrl || image.url
          }
        })
      )
      privateImageUrls.value = urls
      console.log('所有私有图片URL处理完成:', privateImageUrls.value)
    } else {
      console.log('没有图片数据或图片数组为空')
    }
  } catch (error) {
    console.error('加载照片笔记详情失败:', error)
    showToast('加载失败')
  } finally {
    loading.value = false
  }
}

const previewPhoto = (index) => {
  previewIndex.value = index
  showPreview.value = true
}

const onPreviewChange = (index) => {
  previewIndex.value = index
}

const goToUserProfile = () => {
  router.push('/user/' + noteDetail.value.userId)
}

const shareNote = () => {
  const options = [
    { name: '微信', icon: 'wechat' },
    { name: '微博', icon: 'weibo' },
    { name: '复制链接', icon: 'link' }
  ]

  showShareSheet({
    options,
    onSelect: (option) => {
      showToast('分享到' + option.name)
    }
  })
}

const formatTime = (time) => {
  return formatRelativeTime(new Date(time))
}

// 生命周期
onMounted(() => {
  console.log('PhotoNoteDetail组件已挂载')
  console.log('路由参数:', route.params)
  console.log('当前路径:', route.path)
  loadNoteDetail()
})
</script>

<style scoped>
.photo-note-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.detail-navbar {
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.note-content {
  background-color: #fff;
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  margin-right: 12px;
  cursor: pointer;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  cursor: pointer;
}

.publish-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.photo-section {
  padding: 0 16px;
}

.photo-grid {
  display: grid;
  gap: 4px;
  margin-bottom: 16px;
}

.grid-1 {
  grid-template-columns: 1fr;
}

.grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
}

.photo-item {
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.content-section {
  padding: 0 16px 16px;
}

.note-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.note-content-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.error-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  color: #999;
}
</style>
