package com.phototagmoment.dto;

import lombok.Data;

/**
 * 上传凭证DTO
 */
@Data
public class UploadTokenDTO {

    /**
     * 上传凭证
     */
    private String token;

    /**
     * 上传域名
     */
    private String domain;

    /**
     * 存储区域
     */
    private String region;

    /**
     * 凭证过期时间（秒）
     */
    private Long expires;

    /**
     * 最大文件大小（字节）
     */
    private Long maxFileSize;

    /**
     * 允许的文件类型
     */
    private String[] allowedTypes;

    public UploadTokenDTO() {}

    public UploadTokenDTO(String token, String domain, String region, Long expires, Long maxFileSize, String[] allowedTypes) {
        this.token = token;
        this.domain = domain;
        this.region = region;
        this.expires = expires;
        this.maxFileSize = maxFileSize;
        this.allowedTypes = allowedTypes;
    }
}
