import request from '@/utils/request'
import type { AxiosProgressEvent } from 'axios'
import { mockPhotoDetail } from '@/utils/mockApi'
// 导入axios用于直接上传到七牛云
import axios from 'axios'

// 是否使用模拟数据
const USE_MOCK = false

/**
 * 提及用户
 */
export interface MentionUser {
  userId: number
  username: string
  nickname?: string
}

/**
 * 照片上传参数
 */
export interface PhotoUploadParams {
  title: string
  description?: string
  location?: string
  tags?: string[]
  mentions?: MentionUser[]
  visibility: number
  allowComment: boolean | number // 可以是布尔值或整数
  allowDownload: boolean | number // 可以是布尔值或整数
  originalFilename?: string // 原始文件名
}

/**
 * 照片详情
 */
export interface PhotoDetail {
  id: number
  userId: number
  username: string
  nickname: string
  avatar: string
  title: string
  description: string
  url: string
  thumbnailUrl: string
  width: number
  height: number
  location: string
  takenTime: string
  visibility: number
  allowComment: boolean
  allowDownload: boolean
  likeCount: number
  commentCount: number
  collectCount: number
  viewCount: number
  tags: string[]
  isLiked: boolean
  isCollected: boolean
  createdAt: string
}

/**
 * 照片列表参数
 */
export interface PhotoListParams {
  page: number
  size: number
  userId?: number
  tag?: string
  keyword?: string
}

/**
 * 照片列表响应
 */
export interface PhotoListResponse {
  records: PhotoDetail[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 获取七牛云上传凭证
 * @returns 上传凭证信息
 */
export function getUploadToken(): Promise<{
  uploadToken: string;
  key: string;
  domain: string;
  uploadUrl: string;
}> {
  return new Promise((resolve, reject) => {
    request({
      url: '/photo/upload/token',
      method: 'get'
    })
    .then(response => {
      // 检查响应是否包含data字段
      if (response && response.data) {
        console.log('获取上传凭证成功:', response.data);
        resolve(response.data);
      }
      // 检查响应是否直接包含所需字段
      else if (response && (response as any).uploadToken && (response as any).key) {
        console.log('获取上传凭证成功(直接数据):', response);
        resolve(response as any);
      }
      else {
        console.error('获取上传凭证失败，响应格式不正确:', response);
        // 提供更详细的错误信息
        let errorMessage = '获取上传凭证失败';
        if (response && (response as any).message) {
          errorMessage = (response as any).message;
        } else if (response && (response as any).msg) {
          errorMessage = (response as any).msg;
        } else {
          errorMessage = '响应格式不正确，请检查七牛云存储配置';
        }
        reject(new Error(errorMessage));
      }
    })
    .catch(error => {
      console.error('获取上传凭证请求失败:', error);
      // 提供更友好的错误信息
      let errorMessage = '获取上传凭证请求失败';
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      reject(new Error(errorMessage));
    });
  });
}

/**
 * 获取批量上传凭证
 * @param count 照片数量
 * @returns 批量上传凭证信息
 */
export function getBatchUploadToken(count: number): Promise<{
  keys: Record<string, string>;
  tokens: Record<string, string>;
  domain: string;
  uploadUrl: string;
}> {
  return new Promise((resolve, reject) => {
    request({
      url: '/photo/upload/batch-token',
      method: 'get',
      params: { count }
    })
    .then(response => {
      // 检查响应是否包含data字段
      if (response && response.data) {
        console.log('获取批量上传凭证成功:', response.data);
        resolve(response.data);
      }
      // 检查响应是否直接包含所需字段
      else if (response && (response as any).keys && (response as any).tokens) {
        console.log('获取批量上传凭证成功(直接数据):', response);
        resolve(response as any);
      }
      else {
        console.error('获取批量上传凭证失败，响应格式不正确:', response);
        // 提供更详细的错误信息
        let errorMessage = '获取批量上传凭证失败';
        if (response && (response as any).message) {
          errorMessage = (response as any).message;
        } else if (response && (response as any).msg) {
          errorMessage = (response as any).msg;
        } else {
          errorMessage = '响应格式不正确，请检查七牛云存储配置';
        }
        reject(new Error(errorMessage));
      }
    })
    .catch(error => {
      console.error('获取批量上传凭证请求失败:', error);
      // 提供更友好的错误信息
      let errorMessage = '获取批量上传凭证请求失败';
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      reject(new Error(errorMessage));
    });
  });
}

/**
 * 上传照片到七牛云
 * @param file 照片文件
 * @param token 上传凭证
 * @param key 文件名
 * @param uploadUrl 上传地址
 * @param onUploadProgress 上传进度回调
 * @returns 上传结果，包含key和hash
 */
/**
 * 使用axios直接上传文件到七牛云
 * @param file 要上传的文件
 * @param token 上传凭证
 * @param key 文件名
 * @param uploadUrl 上传地址
 * @param onUploadProgress 上传进度回调
 * @returns 上传结果，包含key和hash
 */
export function uploadToQiniu(
  file: File,
  token: string,
  key: string,
  uploadUrl: string,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<{key: string, hash: string}> {
  // 打印上传参数
  console.log('七牛云上传参数:', {
    file: file.name,
    fileSize: file.size,
    fileType: file.type,
    token: token ? (token.length > 10 ? token.substring(0, 10) + '...' : token) : 'null',
    key,
    uploadUrl
  });

  if (!token || token.trim() === '') {
    return Promise.reject(new Error('上传凭证为空'));
  }

  if (!key || key.trim() === '') {
    return Promise.reject(new Error('文件名为空'));
  }

  if (!uploadUrl || uploadUrl.trim() === '') {
    return Promise.reject(new Error('上传地址为空'));
  }

  // 检查文件大小
  if (file.size > 10 * 1024 * 1024) {
    return Promise.reject(new Error('文件大小不能超过10MB'));
  }

  return new Promise((resolve, reject) => {
    try {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      formData.append('token', token);
      formData.append('key', key);

      // 使用axios直接上传到七牛云
      axios.post(uploadUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress,
        timeout: 60000 // 60秒超时
      })
      .then(response => {
        console.log('七牛云上传成功，响应:', response.data);

        // 七牛云上传成功后会返回key和hash
        resolve({
          key: response.data.key || key,
          hash: response.data.hash || ''
        });
      })
      .catch(error => {
        console.error('七牛云上传失败:', error);

        // 提取更详细的错误信息
        let errorMessage = '上传失败';
        if (error.response) {
          console.error('七牛云错误响应:', error.response.data);
          errorMessage = `上传失败: 状态码 ${error.response.status}`;
          if (error.response.data && error.response.data.error) {
            errorMessage += `, ${error.response.data.error}`;
          }
        } else if (error.request) {
          errorMessage = '上传失败: 未收到响应';
        } else {
          errorMessage = `上传失败: ${error.message}`;
        }

        reject(new Error(errorMessage));
      });
    } catch (error) {
      console.error('七牛云上传异常:', error);
      reject(new Error(`上传异常: ${(error as Error).message || '未知错误'}`));
    }
  })
}

/**
 * 保存照片信息
 * @param params 照片参数
 * @returns 响应对象，包含照片ID
 */
export function savePhotoInfo(params: PhotoUploadParams & {
  url: string;
  thumbnailUrl: string;
  width: number;
  height: number;
  key: string;
}): Promise<any> {
  // 从key中提取文件名
  const originalFilename = params.originalFilename || params.key.substring(params.key.lastIndexOf('/') + 1);

  // 确保所有参数都是正确的类型
  const processedParams = {
    ...params,
    // 确保 allowComment 和 allowDownload 是整数
    allowComment: typeof params.allowComment === 'boolean' ? (params.allowComment ? 1 : 0) : Number(params.allowComment),
    allowDownload: typeof params.allowDownload === 'boolean' ? (params.allowDownload ? 1 : 0) : Number(params.allowDownload),
    // 确保 visibility 是整数
    visibility: Number(params.visibility),
    // 确保 width 和 height 是整数
    width: Number(params.width),
    height: Number(params.height),
    // 添加原始文件名
    originalFilename
  };

  console.log('保存照片信息，参数:', processedParams);

  return request({
    url: '/photo/save-info',
    method: 'post',
    data: processedParams
  })
}

/**
 * 上传单张照片
 * @param file 照片文件
 * @param params 照片参数
 * @param onUploadProgress 上传进度回调
 * @returns 照片ID
 */
export function uploadPhoto(
  file: File,
  params: PhotoUploadParams,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<number> {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('data', JSON.stringify(params))

  return request({
    url: '/photo/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress
  })
}

/**
 * 批量上传照片
 * @param files 照片文件列表
 * @param params 照片参数列表
 * @param onUploadProgress 上传进度回调
 * @returns 照片ID列表
 */
export function batchUploadPhotos(
  files: File[],
  params: PhotoUploadParams[],
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<number[]> {
  const formData = new FormData()

  files.forEach((file, index) => {
    formData.append('files', file)
  })

  formData.append('data', JSON.stringify(params))

  return request({
    url: '/photo/batch-upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress
  })
}

/**
 * 获取照片详情（兼容照片笔记）
 * @param id 照片ID
 * @param includeGroupPhotos 是否包含同组照片信息
 * @returns 照片详情
 */
export function getPhotoDetail(id: number | any, includeGroupPhotos: boolean = true): Promise<any> {
  // 确保id是数字类型
  const photoId = typeof id === 'object' ? Number(id.id || 0) : Number(id);

  if (USE_MOCK) {
    return mockPhotoDetail(photoId).then(res => res.data)
  }

  // 优先尝试照片笔记API
  return request({
    url: `/photo-notes/${photoId}`,
    method: 'get'
  }).catch(() => {
    // 如果照片笔记API失败，回退到传统照片API
    return request({
      url: `/photo/detail/${photoId}`,
      method: 'get',
      params: {
        includeGroupPhotos
      }
    })
  })
}

/**
 * 获取照片列表
 * @param params 查询参数
 * @returns 照片列表
 */
export function getPhotoList(params: PhotoListParams): Promise<any> {
  return request({
    url: '/photo/list',
    method: 'get',
    params
  })
}

/**
 * 更新照片信息
 * @param id 照片ID
 * @param params 照片参数
 * @returns 是否成功
 */
export function updatePhoto(id: number, params: PhotoUploadParams): Promise<any> {
  return request({
    url: `/photo/${id}`,
    method: 'put',
    data: params
  })
}

/**
 * 删除照片
 * @param id 照片ID
 * @returns 是否成功
 */
export function deletePhoto(id: number): Promise<any> {
  return request({
    url: `/photo/${id}`,
    method: 'delete'
  })
}

/**
 * 点赞照片（兼容照片笔记）
 * @param id 照片ID
 * @returns 是否成功
 */
export function likePhoto(id: number): Promise<any> {
  // 优先尝试照片笔记API
  return request({
    url: `/photo-notes/${id}/like`,
    method: 'post'
  }).catch(() => {
    // 如果照片笔记API失败，回退到传统照片API
    return request({
      url: `/photo/like/${id}`,
      method: 'post'
    })
  })
}

/**
 * 取消点赞照片
 * @param id 照片ID
 * @returns 是否成功
 */
export function unlikePhoto(id: number): Promise<any> {
  return request({
    url: `/photo/unlike/${id}`,
    method: 'post'
  })
}

/**
 * 收藏照片（兼容照片笔记）
 * @param id 照片ID
 * @returns 是否成功
 */
export function collectPhoto(id: number): Promise<any> {
  // 优先尝试照片笔记API
  return request({
    url: `/photo-notes/${id}/collect`,
    method: 'post'
  }).catch(() => {
    // 如果照片笔记API失败，回退到传统照片API
    return request({
      url: `/photo/collect/${id}`,
      method: 'post'
    })
  })
}

/**
 * 取消收藏照片
 * @param id 照片ID
 * @returns 是否成功
 */
export function uncollectPhoto(id: number): Promise<any> {
  return request({
    url: `/photo/uncollect/${id}`,
    method: 'post'
  })
}

/**
 * 获取照片评论列表
 * @param params 查询参数
 * @returns 评论列表
 */
export function getPhotoComments(params: { photoId: number | any, page: number, size: number }): Promise<any> {
  if (USE_MOCK) {
    return import('@/utils/mockApi').then(({ mockPhotoComments }) => mockPhotoComments(params));
  }

  // 确保photoId是数字类型
  const processedParams = {
    ...params,
    photoId: typeof params.photoId === 'object' ? Number(params.photoId.id || 0) : Number(params.photoId)
  };

  return request({
    url: '/photo/comments',
    method: 'get',
    params: processedParams
  })
}

/**
 * 添加照片评论
 * @param params 评论参数
 * @returns 评论ID
 */
export function addPhotoComment(params: { photoId: number | any, content: string }): Promise<any> {
  if (USE_MOCK) {
    return import('@/utils/mockApi').then(({ mockAddPhotoComment }) => mockAddPhotoComment(params));
  }

  // 确保photoId是数字类型
  const processedParams = {
    ...params,
    photoId: typeof params.photoId === 'object' ? Number(params.photoId.id || 0) : Number(params.photoId)
  };

  return request({
    url: '/comment/add',
    method: 'post',
    data: processedParams
  })
}

/**
 * 点赞评论
 * @param id 评论ID
 * @returns 是否成功
 */
export function likeComment(id: number): Promise<any> {
  if (USE_MOCK) {
    return import('@/utils/mockApi').then(({ mockLikeComment }) => mockLikeComment(id));
  }
  return request({
    url: `/comment/like/${id}`,
    method: 'post'
  })
}

/**
 * 取消点赞评论
 * @param id 评论ID
 * @returns 是否成功
 */
export function unlikeComment(id: number): Promise<any> {
  return request({
    url: `/comment/unlike/${id}`,
    method: 'post'
  })
}

/**
 * 获取照片分组
 * @param groupId 分组ID
 * @returns 照片列表
 */
export function getPhotosByGroupId(groupId: string): Promise<any> {
  return request({
    url: `/photo/group/${groupId}`,
    method: 'get'
  })
}

// ==================== 照片笔记相关API ====================

/**
 * 照片笔记数据接口
 */
export interface PhotoNoteData {
  title: string
  description?: string
  location?: string
  tags?: string[]
  mentions?: MentionUser[]
  visibility: number
  allowComment: boolean | number
  allowDownload: boolean | number
  photos: Array<{
    url: string
    thumbnailUrl: string
    width: number
    height: number
    key: string
  }>
}

/**
 * 发布照片笔记
 * @param data 照片笔记数据
 * @returns Promise
 */
export function publishPhotoNote(data: PhotoNoteData): Promise<any> {
  return request({
    url: '/photo-notes/publish',
    method: 'post',
    data
  })
}

/**
 * 获取照片笔记列表
 * @param params 查询参数
 * @returns Promise
 */
export function getPhotoNoteList(params: any): Promise<any> {
  return request({
    url: '/photo-notes/list',
    method: 'get',
    params
  })
}

/**
 * 获取照片笔记详情
 * @param noteId 照片笔记ID
 * @returns Promise
 */
export function getPhotoNoteDetail(noteId: number): Promise<any> {
  return request({
    url: `/photo-notes/${noteId}`,
    method: 'get'
  })
}

/**
 * 根据标签搜索照片笔记
 * @param params 搜索参数
 * @returns Promise
 */
export function searchPhotoNotesByTag(params: {
  tagName: string
  page: number
  size: number
  sortType?: string
}): Promise<any> {
  return request({
    url: `/photo-notes/tag/${encodeURIComponent(params.tagName)}`,
    method: 'get',
    params: {
      page: params.page,
      size: params.size,
      sortType: params.sortType
    }
  })
}

/**
 * 获取用户的照片笔记
 * @param params 查询参数
 * @returns Promise
 */
export function getUserPhotoNotes(params: {
  userId: number
  page: number
  size: number
  visibility?: number
}): Promise<any> {
  return request({
    url: `/photo-notes/user/${params.userId}`,
    method: 'get',
    params: {
      page: params.page,
      size: params.size,
      visibility: params.visibility
    }
  })
}

/**
 * 点赞照片笔记
 * @param noteId 照片笔记ID
 * @returns Promise
 */
export function likePhotoNote(noteId: number): Promise<any> {
  return request({
    url: `/photo-notes/${noteId}/like`,
    method: 'post'
  })
}

/**
 * 取消点赞照片笔记
 * @param noteId 照片笔记ID
 * @returns Promise
 */
export function unlikePhotoNote(noteId: number): Promise<any> {
  return request({
    url: `/photo-notes/${noteId}/like`,
    method: 'delete'
  })
}

/**
 * 收藏照片笔记
 * @param noteId 照片笔记ID
 * @returns Promise
 */
export function collectPhotoNote(noteId: number): Promise<any> {
  return request({
    url: `/photo-notes/${noteId}/collect`,
    method: 'post'
  })
}

/**
 * 取消收藏照片笔记
 * @param noteId 照片笔记ID
 * @returns Promise
 */
export function uncollectPhotoNote(noteId: number): Promise<any> {
  return request({
    url: `/photo-notes/${noteId}/collect`,
    method: 'delete'
  })
}

/**
 * 删除照片笔记
 * @param noteId 照片笔记ID
 * @returns Promise
 */
export function deletePhotoNote(noteId: number): Promise<any> {
  return request({
    url: `/photo-notes/${noteId}`,
    method: 'delete'
  })
}

/**
 * 获取热门照片笔记
 * @param params 查询参数
 * @returns Promise
 */
export function getHotPhotoNotes(params: any): Promise<any> {
  return request({
    url: '/photo-notes/hot',
    method: 'get',
    params
  })
}

/**
 * 获取推荐照片笔记
 * @param params 查询参数
 * @returns Promise
 */
export function getRecommendedPhotoNotes(params: any): Promise<any> {
  return request({
    url: '/photo-notes/recommended',
    method: 'get',
    params
  })
}

/**
 * 搜索照片笔记
 * @param params 搜索参数
 * @returns Promise
 */
export function searchPhotoNotes(params: any): Promise<any> {
  return request({
    url: '/photo-notes/search',
    method: 'get',
    params
  })
}

/**
 * 获取热门标签
 * @param params 查询参数
 * @returns Promise
 */
export function getHotTags(params: any): Promise<any> {
  return request({
    url: '/photo-notes/tags/hot',
    method: 'get',
    params
  })
}

/**
 * 搜索标签
 * @param params 搜索参数
 * @returns Promise
 */
export function searchTags(params: any): Promise<any> {
  return request({
    url: '/photo-notes/tags/search',
    method: 'get',
    params
  })
}

/**
 * 获取照片笔记上传凭证
 * @returns 上传凭证信息
 */
export function getPhotoNoteUploadToken(): Promise<{
  token: string;
  domain: string;
  region: string;
  expires: number;
  maxFileSize: number;
  allowedTypes: string[];
}> {
  return new Promise((resolve, reject) => {
    request({
      url: '/photo-notes/upload-token',
      method: 'get'
    })
    .then(response => {
      // 检查响应是否包含data字段
      if (response && response.data) {
        console.log('获取照片笔记上传凭证成功:', response.data);
        resolve(response.data);
      }
      // 检查响应是否直接包含所需字段
      else if (response && (response as any).token) {
        console.log('获取照片笔记上传凭证成功(直接数据):', response);
        resolve(response as any);
      }
      else {
        console.error('获取照片笔记上传凭证失败，响应格式不正确:', response);
        // 提供更详细的错误信息
        let errorMessage = '获取照片笔记上传凭证失败';
        if (response && (response as any).message) {
          errorMessage = (response as any).message;
        } else if (response && (response as any).msg) {
          errorMessage = (response as any).msg;
        } else {
          errorMessage = '响应格式不正确，请检查七牛云存储配置';
        }
        reject(new Error(errorMessage));
      }
    })
    .catch(error => {
      console.error('获取照片笔记上传凭证请求失败:', error);
      // 提供更友好的错误信息
      let errorMessage = '获取照片笔记上传凭证请求失败';
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      reject(new Error(errorMessage));
    });
  });
}

/**
 * 照片笔记专用：上传照片到七牛云并返回URL信息
 * @param file 照片文件
 * @param onUploadProgress 上传进度回调
 * @returns 照片URL信息
 */
export function uploadPhotoForNote(
  file: File,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<{
  url: string;
  thumbnailUrl: string;
  width: number;
  height: number;
  key: string;
  originalFilename: string;
}> {
  return new Promise(async (resolve, reject) => {
    try {
      // 1. 获取照片笔记上传凭证
      const tokenInfo = await getPhotoNoteUploadToken();

      // 2. 生成文件名
      const datePath = new Date().toISOString().slice(0, 10).replace(/-/g, '/');
      const uuid = Date.now().toString(36) + Math.random().toString(36).substr(2);
      const fileExt = file.name.split('.').pop() || 'jpg';
      const key = `photos/${datePath}/${uuid}.${fileExt}`;

      // 3. 上传到七牛云
      const uploadResult = await uploadToQiniu(
        file,
        tokenInfo.token,
        key,
        'https://upload.qiniup.com', // 七牛云默认上传地址
        onUploadProgress
      );

      // 4. 构造返回的URL信息
      const baseUrl = tokenInfo.domain.endsWith('/') ? tokenInfo.domain.slice(0, -1) : tokenInfo.domain;
      const url = `${baseUrl}/${uploadResult.key}`;
      const thumbnailUrl = `${url}?imageView2/2/w/300/h/300/q/80`;

      // 5. 获取图片尺寸（简单实现）
      const img = new Image();
      img.onload = () => {
        resolve({
          url,
          thumbnailUrl,
          width: img.width,
          height: img.height,
          key: uploadResult.key,
          originalFilename: file.name
        });
      };
      img.onerror = () => {
        // 如果无法获取尺寸，使用默认值
        resolve({
          url,
          thumbnailUrl,
          width: 800,
          height: 600,
          key: uploadResult.key,
          originalFilename: file.name
        });
      };
      img.src = url;

    } catch (error) {
      console.error('照片笔记上传失败:', error);
      reject(error);
    }
  });
}

// ==================== 兼容性别名函数 ====================
// 注意：这些函数已经在文件前面定义过了，这里只是为了说明兼容性映射关系

// getPhotoDetail -> 已存在，映射到照片详情API
// likePhoto -> 已存在，映射到点赞照片API
// collectPhoto -> 已存在，映射到收藏照片API
// getPhotoComments -> 已存在，映射到照片评论API
// addPhotoComment -> 已存在，映射到添加评论API
// likeComment -> 已存在，映射到点赞评论API
// getBatchUploadToken -> 已存在，映射到批量上传Token API
// getUploadToken -> 已存在，映射到上传Token API
// uploadToQiniu -> 已存在，映射到七牛云上传API
// savePhotoInfo -> 已存在，映射到保存照片信息API
// batchUploadPhotos -> 已存在，映射到批量上传照片API
