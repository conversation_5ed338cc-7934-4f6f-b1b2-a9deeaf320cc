package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.dto.PhotoNotePublishDTO;
import com.phototagmoment.dto.TagSearchResultDTO;
import com.phototagmoment.dto.UploadTokenDTO;
import com.phototagmoment.service.PhotoNoteService;
import com.phototagmoment.service.QiniuStorageService;
import com.phototagmoment.util.UserUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 照片笔记控制器
 */
@Slf4j
@RestController
@RequestMapping("/photo-notes")
@Tag(name = "照片笔记管理", description = "照片笔记相关接口")
@Validated
public class PhotoNoteController {

    @Autowired
    private PhotoNoteService photoNoteService;

    @PostMapping("/publish")
    @Operation(summary = "发布照片笔记", description = "用户发布新的照片笔记")
    public ApiResponse<Long> publishPhotoNote(
            @Valid @RequestBody PhotoNotePublishDTO publishDTO,
            HttpServletRequest request) {

        Long userId = UserUtil.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.failed("用户未登录");
        }

        Long noteId = photoNoteService.publishPhotoNote(publishDTO, userId);

        return ApiResponse.success(noteId, "照片笔记发布成功");
    }

    @GetMapping("/list")
    @Operation(summary = "获取照片笔记列表", description = "分页获取照片笔记列表")
    public ApiResponse<IPage<PhotoNoteDTO>> getPhotoNoteList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            @Parameter(description = "用户ID（可选）") @RequestParam(required = false) Long userId,
            @Parameter(description = "状态筛选") @RequestParam(required = false) Integer status,
            HttpServletRequest request) {

        Long currentUserId = UserUtil.getCurrentUserId();
        IPage<PhotoNoteDTO> result = photoNoteService.getPhotoNoteList(page, size, userId, currentUserId, status);

        return ApiResponse.success(result);
    }

    @GetMapping("/{noteId}")
    @Operation(summary = "获取照片笔记详情", description = "根据ID获取照片笔记详情")
    public ApiResponse<PhotoNoteDTO> getPhotoNoteDetail(
            @Parameter(description = "照片笔记ID") @PathVariable @NotNull Long noteId,
            HttpServletRequest request) {

        Long currentUserId = UserUtil.getCurrentUserId();

        // 增加浏览量
        photoNoteService.incrementViewCount(noteId, currentUserId);

        PhotoNoteDTO result = photoNoteService.getPhotoNoteDetail(noteId, currentUserId);
        if (result == null) {
            return ApiResponse.failed("照片笔记不存在");
        }

        return ApiResponse.success(result);
    }

    @GetMapping("/tag/{tagName}")
    @Operation(summary = "根据标签搜索照片笔记", description = "根据标签名称搜索相关的照片笔记")
    public ApiResponse<TagSearchResultDTO> searchPhotoNotesByTag(
            @Parameter(description = "标签名称") @PathVariable @NotBlank String tagName,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            @Parameter(description = "排序类型") @RequestParam(defaultValue = "hot") String sortType,
            HttpServletRequest request) {

        Long currentUserId = UserUtil.getCurrentUserId();
        TagSearchResultDTO result = photoNoteService.searchPhotoNotesByTag(tagName, page, size, sortType, currentUserId);

        return ApiResponse.success(result);
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户的照片笔记", description = "获取指定用户的照片笔记列表")
    public ApiResponse<IPage<PhotoNoteDTO>> getUserPhotoNotes(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            @Parameter(description = "可见性筛选") @RequestParam(required = false) Integer visibility,
            HttpServletRequest request) {

        Long currentUserId = UserUtil.getCurrentUserId();
        IPage<PhotoNoteDTO> result = photoNoteService.getUserPhotoNotes(userId, page, size, currentUserId, visibility);

        return ApiResponse.success(result);
    }

    @PostMapping("/{noteId}/like")
    @Operation(summary = "点赞照片笔记", description = "用户点赞照片笔记")
    public ApiResponse<Void> likePhotoNote(
            @Parameter(description = "照片笔记ID") @PathVariable @NotNull Long noteId,
            HttpServletRequest request) {

        Long userId = UserUtil.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.failed("用户未登录");
        }

        boolean success = photoNoteService.likePhotoNote(noteId, userId);

        if (success) {
            return ApiResponse.success(null, "点赞成功");
        } else {
            return ApiResponse.failed("点赞失败，可能已经点赞过了");
        }
    }

    @DeleteMapping("/{noteId}/like")
    @Operation(summary = "取消点赞照片笔记", description = "用户取消点赞照片笔记")
    public ApiResponse<Void> unlikePhotoNote(
            @Parameter(description = "照片笔记ID") @PathVariable @NotNull Long noteId,
            HttpServletRequest request) {

        Long userId = UserUtil.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.failed("用户未登录");
        }

        boolean success = photoNoteService.unlikePhotoNote(noteId, userId);

        if (success) {
            return ApiResponse.success(null, "取消点赞成功");
        } else {
            return ApiResponse.failed("取消点赞失败，可能未点赞过");
        }
    }

    @PostMapping("/{noteId}/collect")
    @Operation(summary = "收藏照片笔记", description = "用户收藏照片笔记")
    public ApiResponse<Void> collectPhotoNote(
            @Parameter(description = "照片笔记ID") @PathVariable @NotNull Long noteId,
            HttpServletRequest request) {

        Long userId = UserUtil.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.failed("用户未登录");
        }

        boolean success = photoNoteService.collectPhotoNote(noteId, userId);

        if (success) {
            return ApiResponse.success(null, "收藏成功");
        } else {
            return ApiResponse.failed("收藏失败，可能已经收藏过了");
        }
    }

    @DeleteMapping("/{noteId}/collect")
    @Operation(summary = "取消收藏照片笔记", description = "用户取消收藏照片笔记")
    public ApiResponse<Void> uncollectPhotoNote(
            @Parameter(description = "照片笔记ID") @PathVariable @NotNull Long noteId,
            HttpServletRequest request) {

        Long userId = UserUtil.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.failed("用户未登录");
        }

        boolean success = photoNoteService.uncollectPhotoNote(noteId, userId);

        if (success) {
            return ApiResponse.success(null, "取消收藏成功");
        } else {
            return ApiResponse.failed("取消收藏失败，可能未收藏过");
        }
    }

    @DeleteMapping("/{noteId}")
    @Operation(summary = "删除照片笔记", description = "用户删除自己的照片笔记")
    public ApiResponse<Void> deletePhotoNote(
            @Parameter(description = "照片笔记ID") @PathVariable @NotNull Long noteId,
            HttpServletRequest request) {

        Long userId = UserUtil.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.failed("用户未登录");
        }

        boolean success = photoNoteService.deletePhotoNote(noteId, userId);

        if (success) {
            return ApiResponse.success(null, "删除成功");
        } else {
            return ApiResponse.failed("删除失败，照片笔记不存在或无权限");
        }
    }

    @GetMapping("/hot")
    @Operation(summary = "获取热门照片笔记", description = "获取热门照片笔记列表")
    public ApiResponse<IPage<PhotoNoteDTO>> getHotPhotoNotes(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            @Parameter(description = "最近天数") @RequestParam(defaultValue = "7") @Min(1) @Max(30) Integer days,
            HttpServletRequest request) {

        Long currentUserId = UserUtil.getCurrentUserId();
        IPage<PhotoNoteDTO> result = photoNoteService.getHotPhotoNotes(page, size, days, currentUserId);

        return ApiResponse.success(result);
    }

    @GetMapping("/recommended")
    @Operation(summary = "获取推荐照片笔记", description = "获取个性化推荐的照片笔记列表")
    public ApiResponse<IPage<PhotoNoteDTO>> getRecommendedPhotoNotes(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            HttpServletRequest request) {

        Long userId = UserUtil.getCurrentUserId();
        if (userId == null) {
            return ApiResponse.failed("用户未登录");
        }

        IPage<PhotoNoteDTO> result = photoNoteService.getRecommendedPhotoNotes(page, size, userId);

        return ApiResponse.success(result);
    }

    @GetMapping("/search")
    @Operation(summary = "搜索照片笔记", description = "根据关键词搜索照片笔记")
    public ApiResponse<IPage<PhotoNoteDTO>> searchPhotoNotes(
            @Parameter(description = "搜索关键词") @RequestParam @NotBlank String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            HttpServletRequest request) {

        Long currentUserId = UserUtil.getCurrentUserId();
        IPage<PhotoNoteDTO> result = photoNoteService.searchPhotoNotes(keyword, page, size, currentUserId);

        return ApiResponse.success(result);
    }

    @GetMapping("/tags/hot")
    @Operation(summary = "获取热门标签", description = "获取热门标签列表")
    public ApiResponse<List<String>> getHotTags(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer limit) {

        List<String> result = photoNoteService.getHotTags(limit);
        return ApiResponse.success(result);
    }

    @GetMapping("/tags/search")
    @Operation(summary = "搜索标签", description = "根据关键词搜索标签")
    public ApiResponse<List<String>> searchTags(
            @Parameter(description = "搜索关键词") @RequestParam @NotBlank String keyword,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit) {

        List<String> result = photoNoteService.searchTags(keyword, limit);
        return ApiResponse.success(result);
    }
}
