2025-05-27 15:19:55.226  INFO 54336 --- [main] c.p.PhotoTagMomentApplication            : Starting PhotoTagMomentApplication v0.0.1-SNAPSHOT using Java 17.0.12 on Coffee with PID 54336 (I:\Code\AI\Work\Project\PhotoTagMoment\server\target\phototagmoment-0.0.1-SNAPSHOT.jar started by coffee in I:\Code\AI\Work\Project\PhotoTagMoment)
2025-05-27 15:19:55.237 DEBUG 54336 --- [main] c.p.PhotoTagMomentApplication            : Running with Spring Boot v2.7.17, Spring v5.3.30
2025-05-27 15:19:55.238  INFO 54336 --- [main] c.p.PhotoTagMomentApplication            : No active profile set, falling back to 1 default profile: "default"
2025-05-27 15:19:57.176  INFO 54336 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-27 15:19:57.187  INFO 54336 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-27 15:19:57.276  INFO 54336 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-05-27 15:19:59.005  INFO 54336 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-05-27 15:19:59.025  INFO 54336 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27 15:19:59.025  INFO 54336 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-05-27 15:19:59.210  INFO 54336 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-05-27 15:19:59.211  INFO 54336 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3877 ms
2025-05-27 15:19:59.911  INFO 54336 --- [main] c.p.config.EncryptionConfig              : 用户数据加密密钥验证成功
2025-05-27 15:19:59.920  INFO 54336 --- [main] c.p.config.EncryptionConfig              : RSA密钥对验证成功
2025-05-27 15:20:00.968  INFO 54336 --- [main] com.phototagmoment.config.FlywayConfig   : 初始化 Flyway
2025-05-27 15:20:01.098  INFO 54336 --- [main] com.phototagmoment.config.FlywayConfig   : Flyway 迁移已禁用，因为有版本冲突
2025-05-27 15:20:01.197  INFO 54336 --- [main] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter 初始化完成，userMapper和adminMapper已注入
2025-05-27 15:20:01.253  INFO 54336 --- [main] c.p.security.JwtAuthenticationFilter     : JwtTokenProvider 注入完成
2025-05-27 15:20:01.253  INFO 54336 --- [main] c.p.security.JwtAuthenticationFilter     : tokenPrefix初始化成功: Bearer
2025-05-27 15:20:01.336 DEBUG 54336 --- [main] c.p.security.JwtAuthenticationFilter     : Filter 'jwtAuthenticationFilter' configured for use
2025-05-27 15:20:01.336 DEBUG 54336 --- [main] c.p.s.AdminApiAuthenticationFilter       : Filter 'adminApiAuthenticationFilter' configured for use
2025-05-27 15:20:01.539  INFO 54336 --- [main] c.phototagmoment.config.SecurityConfig   : 创建密码编码器: BCryptPasswordEncoder
2025-05-27 15:20:01.675  INFO 54336 --- [main] com.phototagmoment.config.AuthConfig     : 初始化第三方登录配置
2025-05-27 15:20:01.733  INFO 54336 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27 15:20:02.124  INFO 54336 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27 15:20:02.147 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.207 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: auth.enabled(String)
2025-05-27 15:20:02.322 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.339 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.339 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: auth.qq.enabled(String)
2025-05-27 15:20:02.341 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.342 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.343 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: auth.qq.client-id(String)
2025-05-27 15:20:02.346 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.347 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.348 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: auth.qq.client-secret(String)
2025-05-27 15:20:02.350 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.351 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.352 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: auth.qq.redirect-uri(String)
2025-05-27 15:20:02.353 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.355 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.357 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: auth.wechat.enabled(String)
2025-05-27 15:20:02.358 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.359 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.361 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: auth.wechat.client-id(String)
2025-05-27 15:20:02.363 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.365 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.366 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: auth.wechat.client-secret(String)
2025-05-27 15:20:02.368 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.371 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.372 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: auth.wechat.redirect-uri(String)
2025-05-27 15:20:02.374 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.374  INFO 54336 --- [main] com.phototagmoment.config.AuthConfig     : 从数据库读取第三方登录配置成功
2025-05-27 15:20:02.375  INFO 54336 --- [main] com.phototagmoment.config.AuthConfig     : QQ登录配置: enabled=true, clientId=102792770, redirectUri=http://phototag.aitrpix.com/qq-callback
2025-05-27 15:20:02.375  INFO 54336 --- [main] com.phototagmoment.config.AuthConfig     : 微信登录配置: enabled=true, clientId=, redirectUri=
2025-05-27 15:20:02.420  INFO 54336 --- [main] c.p.config.IdentityVerificationConfig    : 初始化实名认证配置
2025-05-27 15:20:02.422 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.423 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: identity-verification.enabled(String)
2025-05-27 15:20:02.424 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.425 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.426 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: identity-verification.provider(String)
2025-05-27 15:20:02.428 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.429 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.430 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: identity-verification.alipay.app-id(String)
2025-05-27 15:20:02.432 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.433 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.434 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: identity-verification.alipay.private-key(String)
2025-05-27 15:20:02.437 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.437 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.438 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: identity-verification.alipay.public-key(String)
2025-05-27 15:20:02.439 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.441 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.442 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: identity-verification.wechat.app-id(String)
2025-05-27 15:20:02.444 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.449 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.453 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: identity-verification.wechat.app-secret(String)
2025-05-27 15:20:02.459 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.463  INFO 54336 --- [main] c.p.config.IdentityVerificationConfig    : 从数据库读取实名认证配置成功
2025-05-27 15:20:02.464  INFO 54336 --- [main] c.p.config.IdentityVerificationConfig    : 实名认证配置: enabled=false, provider=local
2025-05-27 15:20:02.568 DEBUG 54336 --- [main] c.p.m.F.selectByStorageType              : ==>  Preparing: SELECT * FROM ptm_file_upload_config WHERE storage_type = ? AND enabled = 1 AND is_deleted = 0 ORDER BY sort_order ASC LIMIT 1
2025-05-27 15:20:02.568 DEBUG 54336 --- [main] c.p.m.F.selectByStorageType              : ==> Parameters: QINIU(String)
2025-05-27 15:20:02.575 DEBUG 54336 --- [main] c.p.m.F.selectByStorageType              : <==      Total: 1
2025-05-27 15:20:02.731  INFO 54336 --- [main] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:20:02.731 DEBUG 54336 --- [main] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:20:02.732 DEBUG 54336 --- [main] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:20:02.732  INFO 54336 --- [main] com.phototagmoment.config.QiniuConfig    : 七牛云配置初始化完成，enabled: true
2025-05-27 15:20:02.738  INFO 54336 --- [main] com.phototagmoment.config.SmsConfig      : 初始化短信服务配置
2025-05-27 15:20:02.739 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.740 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: sms.enabled(String)
2025-05-27 15:20:02.741 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.742 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.742 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: sms.provider(String)
2025-05-27 15:20:02.743 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.744 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.745 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: sms.aliyun.access-key-id(String)
2025-05-27 15:20:02.746 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.747 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.747 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: sms.aliyun.access-key-secret(String)
2025-05-27 15:20:02.748 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.749 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.750 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: sms.aliyun.sign-name(String)
2025-05-27 15:20:02.751 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.752 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.753 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: sms.aliyun.template-code(String)
2025-05-27 15:20:02.754 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.755 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.756 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: sms.verification-code.length(String)
2025-05-27 15:20:02.759 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.759 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.760 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: sms.verification-code.expiration(String)
2025-05-27 15:20:02.763 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.765 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.766 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: sms.verification-code.daily-limit(String)
2025-05-27 15:20:02.767 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:02.768 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:02.768 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: sms.verification-code.interval(String)
2025-05-27 15:20:02.770 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 0
2025-05-27 15:20:02.770  INFO 54336 --- [main] com.phototagmoment.config.SmsConfig      : 从数据库读取短信服务配置成功
2025-05-27 15:20:02.770  INFO 54336 --- [main] com.phototagmoment.config.SmsConfig      : 短信服务配置: enabled=false, provider=aliyun
2025-05-27 15:20:02.774  INFO 54336 --- [main] com.phototagmoment.config.StorageConfig  : 初始化存储目录: ./uploads
2025-05-27 15:20:02.775  INFO 54336 --- [main] com.phototagmoment.config.StorageConfig  : 存储目录初始化完成
2025-05-27 15:20:03.633  WARN 54336 --- [main] c.p.service.impl.AliyunSmsServiceImpl    : 阿里云短信服务未启用
2025-05-27 15:20:03.647 DEBUG 54336 --- [main] c.p.m.F.selectByStorageType              : ==>  Preparing: SELECT * FROM ptm_file_upload_config WHERE storage_type = ? AND enabled = 1 AND is_deleted = 0 ORDER BY sort_order ASC LIMIT 1
2025-05-27 15:20:03.647 DEBUG 54336 --- [main] c.p.m.F.selectByStorageType              : ==> Parameters: QINIU(String)
2025-05-27 15:20:03.650 DEBUG 54336 --- [main] c.p.m.F.selectByStorageType              : <==      Total: 1
2025-05-27 15:20:03.652  INFO 54336 --- [main] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:20:03.653 DEBUG 54336 --- [main] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:20:03.653 DEBUG 54336 --- [main] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:20:04.483  INFO 54336 --- [main] c.p.s.impl.QiniuStorageServiceImpl       : 七牛云存储初始化成功
2025-05-27 15:20:05.764 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:05.765 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.baidu.app-id(String)
2025-05-27 15:20:05.767 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:05.769 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:05.770 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.baidu.api-key(String)
2025-05-27 15:20:05.772 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:05.773 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:05.773 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.baidu.secret-key(String)
2025-05-27 15:20:05.775 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:05.776  INFO 54336 --- [main] .p.s.i.BaiduContentModerationServiceImpl : 从数据库获取百度内容审核配置成功: appId=118929147, apiKey=xwH****, secretKey=G6C****
2025-05-27 15:20:05.782 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:05.783 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.connection-timeout(String)
2025-05-27 15:20:05.786 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 0
2025-05-27 15:20:05.790 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:05.799 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.socket-timeout(String)
2025-05-27 15:20:05.804 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 0
2025-05-27 15:20:05.804  INFO 54336 --- [main] .p.s.i.BaiduContentModerationServiceImpl : 从数据库获取连接超时配置成功: connectionTimeout=2000, socketTimeout=60000
2025-05-27 15:20:05.805  INFO 54336 --- [main] .p.s.i.BaiduContentModerationServiceImpl : 百度图像审核服务初始化成功
2025-05-27 15:20:05.957 DEBUG 54336 --- [main] c.p.m.S.getAllEnabledWords               : ==>  Preparing: SELECT * FROM ptm_sensitive_word WHERE status = 1
2025-05-27 15:20:05.958 DEBUG 54336 --- [main] c.p.m.S.getAllEnabledWords               : ==> Parameters: 
2025-05-27 15:20:05.961 DEBUG 54336 --- [main] c.p.m.S.getAllEnabledWords               : <==      Total: 0
2025-05-27 15:20:06.475  INFO 54336 --- [main] c.phototagmoment.config.SecurityConfig   : 创建自定义的AuthenticationManager，完全禁用Spring Security的认证机制
2025-05-27 15:20:07.133  WARN 54336 --- [main] com.phototagmoment.config.AuthConfig     : 微信登录未启用或配置不完整
2025-05-27 15:20:07.135  INFO 54336 --- [main] c.p.service.impl.AuthServiceImpl         : QQ登录服务已注册
2025-05-27 15:20:07.419 DEBUG 54336 --- [main] swordEncoderAuthenticationManagerBuilder : No authenticationProviders and no parentAuthenticationManager defined. Returning null.
2025-05-27 15:20:07.575  INFO 54336 --- [main] c.phototagmoment.config.SecurityConfig   : 配置安全过滤器链
2025-05-27 15:20:07.625  INFO 54336 --- [main] c.phototagmoment.config.SecurityConfig   : 添加JWT过滤器
2025-05-27 15:20:07.626  INFO 54336 --- [main] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter 初始化完成，userMapper和adminMapper已注入
2025-05-27 15:20:07.626  INFO 54336 --- [main] c.p.security.JwtAuthenticationFilter     : JwtTokenProvider 注入完成
2025-05-27 15:20:07.627  INFO 54336 --- [main] c.phototagmoment.config.SecurityConfig   : JWT过滤器添加完成
2025-05-27 15:20:07.627  INFO 54336 --- [main] c.phototagmoment.config.SecurityConfig   : 安全过滤器链配置完成
2025-05-27 15:20:07.690 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for ExactUrl [processUrl='/auth/login?error']
2025-05-27 15:20:07.693 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for ExactUrl [processUrl='/auth/login']
2025-05-27 15:20:07.693 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for ExactUrl [processUrl='/auth/login']
2025-05-27 15:20:07.694 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/admin/system/permission/**']
2025-05-27 15:20:07.695 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/admin/system/role/**']
2025-05-27 15:20:07.696 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/admin/system/sensitive-word/**']
2025-05-27 15:20:07.696 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/admin/system/admin/**']
2025-05-27 15:20:07.697 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/admin/system/log/**']
2025-05-27 15:20:07.697 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/admin/file/**']
2025-05-27 15:20:07.697 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/admin/file-manage/**']
2025-05-27 15:20:07.697 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/admin/file-upload-config/**']
2025-05-27 15:20:07.697 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/system/permission/**']
2025-05-27 15:20:07.697 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/system/role/**']
2025-05-27 15:20:07.698 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/system/system-role/**']
2025-05-27 15:20:07.699 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/system/sensitive-word/**']
2025-05-27 15:20:07.699 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/system/admin/**']
2025-05-27 15:20:07.699 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/system/log/**']
2025-05-27 15:20:07.699 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/file/**']
2025-05-27 15:20:07.699 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/file-manage/**']
2025-05-27 15:20:07.699 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/file-upload-config/**']
2025-05-27 15:20:07.699 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/auth/**']
2025-05-27 15:20:07.700 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/system/login']
2025-05-27 15:20:07.700 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/admin/system/info']
2025-05-27 15:20:07.700 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/recommendation/**']
2025-05-27 15:20:07.700 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/search/**']
2025-05-27 15:20:07.702 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/photo/list/**']
2025-05-27 15:20:07.702 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/photo/detail/**']
2025-05-27 15:20:07.702 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/photo/comments/**']
2025-05-27 15:20:07.703 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/photo-notes/**']
2025-05-27 15:20:07.703 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/tag/**']
2025-05-27 15:20:07.703 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/user/profile/**']
2025-05-27 15:20:07.704 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/photo/group/**']
2025-05-27 15:20:07.705 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/dict/**']
2025-05-27 15:20:07.706 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/notification/**']
2025-05-27 15:20:07.706 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-05-27 15:20:07.706 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-05-27 15:20:07.706 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-05-27 15:20:07.706 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-resources/**']
2025-05-27 15:20:07.706 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-05-27 15:20:07.707 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/doc.html']
2025-05-27 15:20:07.708 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-05-27 15:20:07.708 DEBUG 54336 --- [main] edFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-05-27 15:20:07.737  INFO 54336 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@286090c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@31120021, org.springframework.security.web.context.SecurityContextPersistenceFilter@777d191f, org.springframework.security.web.header.HeaderWriterFilter@77f905e3, com.phototagmoment.security.JwtAuthenticationFilter@3df1a1ac, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@5f631ca0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@31142d58, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e38f0b7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@58606c91, org.springframework.security.web.session.SessionManagementFilter@338766de, org.springframework.security.web.access.ExceptionTranslationFilter@793d163b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6f6c6077]
2025-05-27 15:20:07.814 DEBUG 54336 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping      : Patterns [/ws/notification] in 'webSocketHandlerMapping'
2025-05-27 15:20:08.388 DEBUG 54336 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-27 15:20:08.833  INFO 54336 --- [main] com.phototagmoment.config.WebMvcConfig   : 添加拦截器
2025-05-27 15:20:08.834  INFO 54336 --- [main] com.phototagmoment.config.WebMvcConfig   : 添加管理员API拦截器完成
2025-05-27 15:20:08.835  INFO 54336 --- [main] com.phototagmoment.config.WebMvcConfig   : 拦截器添加完成
2025-05-27 15:20:09.451 DEBUG 54336 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : 330 mappings in 'requestMappingHandlerMapping'
2025-05-27 15:20:09.587 DEBUG 54336 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**, /static/**, /doc.html, /swagger-resources/**, /v3/api-docs/**, /favicon.ico, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-05-27 15:20:09.713 DEBUG 54336 --- [main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-27 15:20:13.170  INFO 54336 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path '/api'
2025-05-27 15:20:13.218  INFO 54336 --- [main] c.p.PhotoTagMomentApplication            : Started PhotoTagMomentApplication in 19.21 seconds (JVM running for 20.18)
2025-05-27 15:20:13.257  INFO 54336 --- [main] c.p.config.AdminInitializer              : 确保 Flyway 迁移已完成
2025-05-27 15:20:13.285  INFO 54336 --- [main] c.p.config.AdminInitializer              : 管理员表已存在，继续初始化
2025-05-27 15:20:13.295 DEBUG 54336 --- [main] c.p.mapper.AdminMapper.selectByUsername  : ==>  Preparing: SELECT * FROM ptm_admin WHERE username = ? AND is_deleted = 0
2025-05-27 15:20:13.297 DEBUG 54336 --- [main] c.p.mapper.AdminMapper.selectByUsername  : ==> Parameters: admin(String)
2025-05-27 15:20:13.302 DEBUG 54336 --- [main] c.p.mapper.AdminMapper.selectByUsername  : <==      Total: 1
2025-05-27 15:20:13.304  INFO 54336 --- [main] c.p.config.AdminInitializer              : 默认管理员账户已存在，跳过初始化
2025-05-27 15:20:13.310  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 开始初始化系统配置...
2025-05-27 15:20:13.364  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: content-moderation.baidu.api-key
2025-05-27 15:20:13.367  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: content-moderation.baidu.secret-key
2025-05-27 15:20:13.370 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.371 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.mode(String)
2025-05-27 15:20:13.373 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.373  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: content-moderation.mode
2025-05-27 15:20:13.374 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.375 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.image.provider(String)
2025-05-27 15:20:13.376 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.380  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: content-moderation.image.provider
2025-05-27 15:20:13.382 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.384 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.text.provider(String)
2025-05-27 15:20:13.396 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.404  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: content-moderation.text.provider
2025-05-27 15:20:13.405  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: content-moderation.baidu.app-id
2025-05-27 15:20:13.407 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.409 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.auto-approve(String)
2025-05-27 15:20:13.411 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.412  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: content-moderation.auto-approve
2025-05-27 15:20:13.416 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.417 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.sensitivity(String)
2025-05-27 15:20:13.418 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.419  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: content-moderation.sensitivity
2025-05-27 15:20:13.420 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.421 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.enabled(String)
2025-05-27 15:20:13.423 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.424  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: content-moderation.enabled
2025-05-27 15:20:13.425 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.426 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: storage.qiniu.download-expires(String)
2025-05-27 15:20:13.429 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.430  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: storage.qiniu.download-expires
2025-05-27 15:20:13.432 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.433 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: storage.type(String)
2025-05-27 15:20:13.434 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.436  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: storage.type
2025-05-27 15:20:13.436 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.437 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: storage.qiniu.bucket(String)
2025-05-27 15:20:13.438 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.439  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: storage.qiniu.bucket
2025-05-27 15:20:13.440 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.440 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: storage.qiniu.secret-key(String)
2025-05-27 15:20:13.442 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.444  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: storage.qiniu.secret-key
2025-05-27 15:20:13.450 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.452 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: storage.qiniu.domain(String)
2025-05-27 15:20:13.453 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.454  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: storage.qiniu.domain
2025-05-27 15:20:13.454 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.455 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: storage.qiniu.region(String)
2025-05-27 15:20:13.456 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.457  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: storage.qiniu.region
2025-05-27 15:20:13.457 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.459 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: storage.qiniu.is-private(String)
2025-05-27 15:20:13.459 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.462  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: storage.qiniu.is-private
2025-05-27 15:20:13.466 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.466 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: storage.qiniu.access-key(String)
2025-05-27 15:20:13.468 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.469  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: storage.qiniu.access-key
2025-05-27 15:20:13.469 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.470 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: storage.qiniu.upload-dir(String)
2025-05-27 15:20:13.471 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.472  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: storage.qiniu.upload-dir
2025-05-27 15:20:13.472  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: auth.wechat.client-secret
2025-05-27 15:20:13.473  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: auth.qq.enabled
2025-05-27 15:20:13.473  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: auth.qq.client-secret
2025-05-27 15:20:13.473  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: auth.enabled
2025-05-27 15:20:13.474  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: auth.wechat.client-id
2025-05-27 15:20:13.474  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: auth.qq.redirect-uri
2025-05-27 15:20:13.474  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: auth.wechat.enabled
2025-05-27 15:20:13.475  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: auth.qq.client-id
2025-05-27 15:20:13.475  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: auth.wechat.redirect-uri
2025-05-27 15:20:13.475  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 第三方登录配置初始化完成
2025-05-27 15:20:13.475  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: identity-verification.alipay.private-key
2025-05-27 15:20:13.479  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: identity-verification.wechat.app-id
2025-05-27 15:20:13.480  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: identity-verification.enabled
2025-05-27 15:20:13.481  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: identity-verification.alipay.app-id
2025-05-27 15:20:13.484  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: identity-verification.provider
2025-05-27 15:20:13.484  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: identity-verification.wechat.app-secret
2025-05-27 15:20:13.485  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: identity-verification.alipay.public-key
2025-05-27 15:20:13.485  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 实名认证配置初始化完成
2025-05-27 15:20:13.486  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: sms.aliyun.access-key-id
2025-05-27 15:20:13.486  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: sms.verification-code.daily-limit
2025-05-27 15:20:13.486  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: sms.aliyun.access-key-secret
2025-05-27 15:20:13.487  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: sms.verification-code.expiration
2025-05-27 15:20:13.487  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: sms.aliyun.template-code
2025-05-27 15:20:13.487  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: sms.enabled
2025-05-27 15:20:13.487  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: sms.aliyun.sign-name
2025-05-27 15:20:13.488  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: sms.verification-code.length
2025-05-27 15:20:13.488  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: sms.provider
2025-05-27 15:20:13.489  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 短信配置初始化完成
2025-05-27 15:20:13.490 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.490 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: wechat.mp.app-id(String)
2025-05-27 15:20:13.492 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.493  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: wechat.mp.app-id
2025-05-27 15:20:13.496 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.496 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: wechat.mp.aes-key(String)
2025-05-27 15:20:13.500 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.501  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: wechat.mp.aes-key
2025-05-27 15:20:13.502 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.503 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: wechat.mini-app.app-id(String)
2025-05-27 15:20:13.504 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.505  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: wechat.mini-app.app-id
2025-05-27 15:20:13.505 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.506 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: wechat.mini-app.aes-key(String)
2025-05-27 15:20:13.507 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.507  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: wechat.mini-app.aes-key
2025-05-27 15:20:13.509 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.510 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: wechat.mp.token(String)
2025-05-27 15:20:13.514 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.517  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: wechat.mp.token
2025-05-27 15:20:13.518 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.520 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: wechat.mini-app.app-secret(String)
2025-05-27 15:20:13.522 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.523  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: wechat.mini-app.app-secret
2025-05-27 15:20:13.524 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.525 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: wechat.enabled(String)
2025-05-27 15:20:13.527 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.530  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: wechat.enabled
2025-05-27 15:20:13.533 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.534 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: wechat.mini-app.token(String)
2025-05-27 15:20:13.538 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.539  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: wechat.mini-app.token
2025-05-27 15:20:13.541 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:20:13.541 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : ==> Parameters: wechat.mp.app-secret(String)
2025-05-27 15:20:13.543 DEBUG 54336 --- [main] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:20:13.545  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 配置已存在，跳过: wechat.mp.app-secret
2025-05-27 15:20:13.546  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 微信配置初始化完成
2025-05-27 15:20:13.546  INFO 54336 --- [main] c.p.config.ConfigInitializer             : 系统配置初始化完成
2025-05-27 15:20:13.546  INFO 54336 --- [main] c.p.config.DatabaseInitializer           : 开始初始化数据库结构...
2025-05-27 15:20:13.553  INFO 54336 --- [main] c.p.config.DatabaseInitializer           : 数据库结构初始化完成
2025-05-27 15:20:13.559  INFO 54336 --- [main] c.phototagmoment.config.UserInitializer  : 确保 Flyway 迁移已完成
2025-05-27 15:20:13.565  INFO 54336 --- [main] c.phototagmoment.config.UserInitializer  : 用户表已存在，继续初始化
2025-05-27 15:20:13.566 DEBUG 54336 --- [main] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:20:13.568 DEBUG 54336 --- [main] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:20:13.571 DEBUG 54336 --- [main] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:20:13.581  WARN 54336 --- [main] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:20:13.582  INFO 54336 --- [main] c.phototagmoment.config.UserInitializer  : 默认测试用户账户已存在，跳过初始化
2025-05-27 15:21:15.870  INFO 54336 --- [http-nio-8081-exec-2] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-05-27 15:21:15.882  INFO 54336 --- [http-nio-8081-exec-3] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27 15:21:15.882  INFO 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27 15:21:15.882 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Detected StandardServletMultipartResolver
2025-05-27 15:21:15.882 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Detected AcceptHeaderLocaleResolver
2025-05-27 15:21:15.883 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Detected FixedThemeResolver
2025-05-27 15:21:15.884 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@59939293
2025-05-27 15:21:15.885 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.support.SessionFlashMapManager@5d94a2dc
2025-05-27 15:21:15.885 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-05-27 15:21:15.885  INFO 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-05-27 15:21:15.911 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /dict/data/code/photo_category?_t=1748330475747
2025-05-27 15:21:15.911 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /search/popular-tags?limit=10&_t=1748330475747
2025-05-27 15:21:15.911 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/interest-tags?limit=15&_t=1748330475747
2025-05-27 15:21:15.911 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330475747
2025-05-27 15:21:15.920 DEBUG 54336 --- [http-nio-8081-exec-2] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:21:15.920 DEBUG 54336 --- [http-nio-8081-exec-3] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:21:15.920 DEBUG 54336 --- [http-nio-8081-exec-1] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:21:15.920 DEBUG 54336 --- [http-nio-8081-exec-5] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:21:15.925  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:21:15.926 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/home
2025-05-27 15:21:15.925  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/dict/data/code/photo_category
2025-05-27 15:21:15.927  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:21:15.930 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/dict/data/code/photo_category
2025-05-27 15:21:15.930  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:21:15.931  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /dict
2025-05-27 15:21:15.931  WARN 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : tokenHeader为null，请检查配置文件中的jwt.header属性
2025-05-27 15:21:15.931  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:21:15.932  WARN 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : tokenPrefix为null，请检查配置文件中的jwt.token-prefix属性
2025-05-27 15:21:15.932  WARN 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : tokenPrefix为null，请检查配置文件中的jwt.token-prefix属性
2025-05-27 15:21:15.932  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/search/popular-tags
2025-05-27 15:21:15.934 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/search/popular-tags
2025-05-27 15:21:15.935  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /search
2025-05-27 15:21:15.936  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:21:15.936  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:21:15.932  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/interest-tags
2025-05-27 15:21:15.936 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/interest-tags
2025-05-27 15:21:15.935  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:21:15.935  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:21:15.943  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.943  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:21:15.943  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:21:15.943  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:21:15.943  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:21:15.943  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:21:15.944  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:21:15.936  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.942  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:21:15.947  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:21:15.942  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.945  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.949  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:21:15.950  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:21:15.950  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:21:15.950  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:21:15.950  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:21:15.946  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:21:15.948  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:21:15.948  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:21:15.953  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.951  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.953  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:21:15.954  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:21:15.954  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:21:15.954  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.955  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.955  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:21:15.955  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/dict/data/code/photo_category
2025-05-27 15:21:15.952  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:21:15.953  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:21:15.953  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:21:15.956  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:21:15.958  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:21:15.958  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:21:15.958  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:21:15.958  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.959  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:21:15.959  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:21:15.956  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:21:15.956  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:21:15.959  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:21:15.959  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:21:15.959  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:21:15.959  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:21:15.959  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:21:15.960  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:21:15.960  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:21:15.963  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.964  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:21:15.964  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:21:15.962  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:21:15.963  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:21:15.964  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:21:15.965  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.966  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:21:15.966  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.966  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.966  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:21:15.966  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/home
2025-05-27 15:21:15.966  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:21:15.966  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.969  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:21:15.968  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:21:15.969  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:21:15.969  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:21:15.969  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:21:15.970  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:21:15.971  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:21:15.972  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.983  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:21:15.970  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:21:15.984  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:21:15.986  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:21:15.986  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.986  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.986  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:21:15.986  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/search/popular-tags
2025-05-27 15:21:15.985  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:21:15.988  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.989  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:21:15.992  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:21:15.993  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:21:15.994  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.996  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:15.997  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:21:15.997  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/interest-tags
2025-05-27 15:21:16.088  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:21:16.088  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:21:16.088  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:21:16.088  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:21:16.092 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:21:16.093  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:21:16.093 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:21:16.094 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:21:16.094 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:21:16.096  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:21:16.095  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:21:16.096  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:21:16.096 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:21:16.096 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:21:16.096 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:21:16.096 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:21:16.098 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:21:16.098 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:21:16.098 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:21:16.098 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:21:16.101 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:21:16.101 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:21:16.101 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:21:16.101 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:21:16.102  WARN 54336 --- [http-nio-8081-exec-1] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:21:16.102  WARN 54336 --- [http-nio-8081-exec-3] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:21:16.103  WARN 54336 --- [http-nio-8081-exec-2] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:21:16.103  WARN 54336 --- [http-nio-8081-exec-5] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:21:16.103  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:21:16.103  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:21:16.103  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:21:16.103  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:21:16.107  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:21:16.107  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:21:16.107  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:21:16.107  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:21:16.108  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:21:16.108  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:21:16.108  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:21:16.108  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:21:16.109  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/dict/data/code/photo_category
2025-05-27 15:21:16.110  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/interest-tags
2025-05-27 15:21:16.110  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/home
2025-05-27 15:21:16.112  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/search/popular-tags
2025-05-27 15:21:16.131 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/interest-tags?limit=15&_t=1748330475747] with attributes [permitAll]
2025-05-27 15:21:16.131 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330475747] with attributes [permitAll]
2025-05-27 15:21:16.131 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /search/popular-tags?limit=10&_t=1748330475747] with attributes [permitAll]
2025-05-27 15:21:16.131 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /dict/data/code/photo_category?_t=1748330475747] with attributes [permitAll]
2025-05-27 15:21:16.133 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /dict/data/code/photo_category?_t=1748330475747
2025-05-27 15:21:16.133 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /search/popular-tags?limit=10&_t=1748330475747
2025-05-27 15:21:16.133 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/interest-tags?limit=15&_t=1748330475747
2025-05-27 15:21:16.133 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330475747
2025-05-27 15:21:16.133  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/search/popular-tags
2025-05-27 15:21:16.133  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/dict/data/code/photo_category
2025-05-27 15:21:16.133  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/interest-tags
2025-05-27 15:21:16.134  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:21:16.134  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/search/popular-tags
2025-05-27 15:21:16.134  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/dict/data/code/photo_category
2025-05-27 15:21:16.134  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/interest-tags
2025-05-27 15:21:16.134  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/home
2025-05-27 15:21:16.140 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/home?page=1&size=20&category=recommend&_t=1748330475747", parameters={masked}
2025-05-27 15:21:16.140 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : GET "/api/dict/data/code/photo_category?_t=1748330475747", parameters={masked}
2025-05-27 15:21:16.140 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.web.servlet.DispatcherServlet        : GET "/api/search/popular-tags?limit=10&_t=1748330475747", parameters={masked}
2025-05-27 15:21:16.140 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/interest-tags?limit=15&_t=1748330475747", parameters={masked}
2025-05-27 15:21:16.159 DEBUG 54336 --- [http-nio-8081-exec-2] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getHomeRecommendations(int, int)
2025-05-27 15:21:16.159 DEBUG 54336 --- [http-nio-8081-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getUserInterestTags(int)
2025-05-27 15:21:16.159 DEBUG 54336 --- [http-nio-8081-exec-5] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.SearchController#getPopularTags(Integer)
2025-05-27 15:21:16.164 DEBUG 54336 --- [http-nio-8081-exec-3] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.DictController#getDataByType(String)
2025-05-27 15:21:16.193 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.m.PhotoTagMapper.getPopularTags      : ==>  Preparing: SELECT tag_name as name, COUNT(photo_id) AS count FROM ptm_photo_tag GROUP BY tag_name ORDER BY count DESC LIMIT ?
2025-05-27 15:21:16.195 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.m.PhotoTagMapper.getPopularTags      : ==> Parameters: 10(Integer)
2025-05-27 15:21:16.203 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.m.PhotoTagMapper.getPopularTags      : <==      Total: 5
2025-05-27 15:21:16.207  INFO 54336 --- [http-nio-8081-exec-2] c.p.s.impl.RecommendationServiceImpl     : 获取热门照片笔记，页码：1，每页大小：20
2025-05-27 15:21:16.215 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.DictDataMapper.getByDictType  : ==>  Preparing: SELECT d.* FROM ptm_dict_data d JOIN ptm_dict_type t ON d.dict_type_id = t.id WHERE t.dict_type = ? AND d.status = 1 AND d.is_deleted = 0 AND t.is_deleted = 0 ORDER BY d.dict_sort
2025-05-27 15:21:16.216 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.DictDataMapper.getByDictType  : ==> Parameters: photo_category(String)
2025-05-27 15:21:16.218 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.DictDataMapper.getByDictType  : <==      Total: 0
2025-05-27 15:21:16.250 DEBUG 54336 --- [http-nio-8081-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:21:16.250 DEBUG 54336 --- [http-nio-8081-exec-5] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:21:16.250 DEBUG 54336 --- [http-nio-8081-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:21:16.255 DEBUG 54336 --- [http-nio-8081-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=[风景, 美食, 旅行, 人像, 宠物, 城市, 自然, 建筑, 艺术, 生活])]
2025-05-27 15:21:16.263 DEBUG 54336 --- [http-nio-8081-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=[], timestamp=1748330476229)]
2025-05-27 15:21:16.264 DEBUG 54336 --- [http-nio-8081-exec-5] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=[TagDTO(name=PhotoTag成都, count=36), TagDTO(name=PhotoTag, c (truncated)...]
2025-05-27 15:21:16.295 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:21:16.295 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:21:16.295 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:21:16.296 DEBUG 54336 --- [http-nio-8081-exec-5] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:21:16.296 DEBUG 54336 --- [http-nio-8081-exec-1] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:21:16.296 DEBUG 54336 --- [http-nio-8081-exec-3] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:21:16.406 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes_mpCount      : ==>  Preparing: SELECT COUNT(*) AS total FROM ptm_photo_note pn WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
2025-05-27 15:21:16.407 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes_mpCount      : ==> Parameters: 7(Integer)
2025-05-27 15:21:16.410 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes_mpCount      : <==      Total: 1
2025-05-27 15:21:16.415 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes              : ==>  Preparing: SELECT pn.id, pn.user_id, u.nickname, u.avatar, pn.title, pn.content, pn.photo_count, pn.view_count, pn.like_count, pn.comment_count, pn.share_count, pn.visibility, pn.allow_comment, pn.status, pn.reject_reason, pn.location, pn.longitude, pn.latitude, pn.created_at, pn.updated_at ,false as is_liked, false as is_collected FROM ptm_photo_note pn LEFT JOIN ptm_user u ON pn.user_id = u.id WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC LIMIT ?
2025-05-27 15:21:16.416 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes              : ==> Parameters: 7(Integer), 20(Long)
2025-05-27 15:21:16.418 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes              : <==      Total: 3
2025-05-27 15:21:16.425 DEBUG 54336 --- [http-nio-8081-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:21:16.425 DEBUG 54336 --- [http-nio-8081-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@66731 (truncated)...]
2025-05-27 15:21:16.439 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:21:16.439 DEBUG 54336 --- [http-nio-8081-exec-2] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:21:16.445 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330475747
2025-05-27 15:21:16.446 DEBUG 54336 --- [http-nio-8081-exec-4] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:21:16.446  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:21:16.447 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/home
2025-05-27 15:21:16.447  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:21:16.447  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:21:16.447  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:21:16.447  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:21:16.448  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:21:16.449  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:21:16.449  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:16.449  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:21:16.449  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:21:16.449  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:21:16.449  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:16.449  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:21:16.450  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:21:16.450  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/home
2025-05-27 15:21:16.452  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:21:16.457 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:21:16.457  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:21:16.457 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:21:16.459 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:21:16.460 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:21:16.461  WARN 54336 --- [http-nio-8081-exec-4] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:21:16.462  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:21:16.462  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:21:16.462  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:21:16.463  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/home
2025-05-27 15:21:16.463 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330475747] with attributes [permitAll]
2025-05-27 15:21:16.463 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330475747
2025-05-27 15:21:16.464  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:21:16.464  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/home
2025-05-27 15:21:16.464 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/home?page=1&size=20&category=recommend&_t=1748330475747", parameters={masked}
2025-05-27 15:21:16.464 DEBUG 54336 --- [http-nio-8081-exec-4] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getHomeRecommendations(int, int)
2025-05-27 15:21:16.465  INFO 54336 --- [http-nio-8081-exec-4] c.p.s.impl.RecommendationServiceImpl     : 获取热门照片笔记，页码：1，每页大小：20
2025-05-27 15:21:16.474 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.m.P.selectHotPhotoNotes_mpCount      : ==>  Preparing: SELECT COUNT(*) AS total FROM ptm_photo_note pn WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
2025-05-27 15:21:16.475 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.m.P.selectHotPhotoNotes_mpCount      : ==> Parameters: 7(Integer)
2025-05-27 15:21:16.476 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.m.P.selectHotPhotoNotes_mpCount      : <==      Total: 1
2025-05-27 15:21:16.477 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.m.P.selectHotPhotoNotes              : ==>  Preparing: SELECT pn.id, pn.user_id, u.nickname, u.avatar, pn.title, pn.content, pn.photo_count, pn.view_count, pn.like_count, pn.comment_count, pn.share_count, pn.visibility, pn.allow_comment, pn.status, pn.reject_reason, pn.location, pn.longitude, pn.latitude, pn.created_at, pn.updated_at ,false as is_liked, false as is_collected FROM ptm_photo_note pn LEFT JOIN ptm_user u ON pn.user_id = u.id WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC LIMIT ?
2025-05-27 15:21:16.479 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.m.P.selectHotPhotoNotes              : ==> Parameters: 7(Integer), 20(Long)
2025-05-27 15:21:16.482 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.m.P.selectHotPhotoNotes              : <==      Total: 3
2025-05-27 15:21:16.482 DEBUG 54336 --- [http-nio-8081-exec-4] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:21:16.483 DEBUG 54336 --- [http-nio-8081-exec-4] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@2c1ae (truncated)...]
2025-05-27 15:21:16.484 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:21:16.484 DEBUG 54336 --- [http-nio-8081-exec-4] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:05.498 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /photo/upload/batch-token?count=9&_t=1748330525491
2025-05-27 15:22:05.498 DEBUG 54336 --- [http-nio-8081-exec-6] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:05.498  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo/upload/batch-token
2025-05-27 15:22:05.500 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/photo/upload/batch-token
2025-05-27 15:22:05.500  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:05.500  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:05.500  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:05.500  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:05.500  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:05.501  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/upload
2025-05-27 15:22:05.501  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:05.501  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:05.501  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:05.501  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:05.501  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:05.501  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:05.501  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:05.502  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:05.502  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:05.502  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:05.502  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:05.502  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:05.502  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:05.502  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:05.503  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:05.503  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:05.503  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/upload/batch-token
2025-05-27 15:22:05.505  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:05.506 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:05.506  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:05.507 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:05.507 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:05.508 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:05.509  WARN 54336 --- [http-nio-8081-exec-6] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:05.509  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:05.509  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:05.510  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:05.511 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /photo/upload/batch-token?count=9&_t=1748330525491] with attributes [authenticated]
2025-05-27 15:22:05.511 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /photo/upload/batch-token?count=9&_t=1748330525491
2025-05-27 15:22:05.511  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo/upload/batch-token
2025-05-27 15:22:05.511  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/photo/upload/batch-token
2025-05-27 15:22:05.512 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.web.servlet.DispatcherServlet        : GET "/api/photo/upload/batch-token?count=9&_t=1748330525491", parameters={masked}
2025-05-27 15:22:05.512 DEBUG 54336 --- [http-nio-8081-exec-6] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoUploadController#getBatchUploadToken(Integer)
2025-05-27 15:22:05.515 DEBUG 54336 --- [http-nio-8081-exec-6] horizationManagerBeforeMethodInterceptor : Authorizing method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoUploadController.getBatchUploadToken(java.lang.Integer); target is of class [com.phototagmoment.controller.PhotoUploadController]
2025-05-27 15:22:05.519 DEBUG 54336 --- [http-nio-8081-exec-6] horizationManagerBeforeMethodInterceptor : Authorized method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoUploadController.getBatchUploadToken(java.lang.Integer); target is of class [com.phototagmoment.controller.PhotoUploadController]
2025-05-27 15:22:05.522 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.F.selectByStorageType              : ==>  Preparing: SELECT * FROM ptm_file_upload_config WHERE storage_type = ? AND enabled = 1 AND is_deleted = 0 ORDER BY sort_order ASC LIMIT 1
2025-05-27 15:22:05.523 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.F.selectByStorageType              : ==> Parameters: QINIU(String)
2025-05-27 15:22:05.524 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.F.selectByStorageType              : <==      Total: 1
2025-05-27 15:22:05.536  INFO 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:22:05.536 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:22:05.538 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:22:05.538 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:05.538 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:05.539 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:05.541 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:05.541  WARN 54336 --- [http-nio-8081-exec-6] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:05.541 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:05.549  INFO 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:22:05.549 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:22:05.550 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:22:05.566  INFO 54336 --- [http-nio-8081-exec-6] c.p.s.impl.QiniuStorageServiceImpl       : 生成上传凭证成功，文件名: phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910
2025-05-27 15:22:05.570  INFO 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:22:05.570 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:22:05.570 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:22:05.571  INFO 54336 --- [http-nio-8081-exec-6] c.p.s.impl.QiniuStorageServiceImpl       : 生成上传凭证成功，文件名: phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63
2025-05-27 15:22:05.571  INFO 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:22:05.571 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:22:05.571 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:22:05.572  INFO 54336 --- [http-nio-8081-exec-6] c.p.s.impl.QiniuStorageServiceImpl       : 生成上传凭证成功，文件名: phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6
2025-05-27 15:22:05.572  INFO 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:22:05.572 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:22:05.572 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:22:05.573  INFO 54336 --- [http-nio-8081-exec-6] c.p.s.impl.QiniuStorageServiceImpl       : 生成上传凭证成功，文件名: phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7
2025-05-27 15:22:05.573  INFO 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:22:05.573 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:22:05.573 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:22:05.574  INFO 54336 --- [http-nio-8081-exec-6] c.p.s.impl.QiniuStorageServiceImpl       : 生成上传凭证成功，文件名: phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645
2025-05-27 15:22:05.574  INFO 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:22:05.575 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:22:05.575 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:22:05.575  INFO 54336 --- [http-nio-8081-exec-6] c.p.s.impl.QiniuStorageServiceImpl       : 生成上传凭证成功，文件名: phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38
2025-05-27 15:22:05.576  INFO 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:22:05.576 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:22:05.576 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:22:05.577  INFO 54336 --- [http-nio-8081-exec-6] c.p.s.impl.QiniuStorageServiceImpl       : 生成上传凭证成功，文件名: phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca
2025-05-27 15:22:05.577  INFO 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:22:05.577 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:22:05.577 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:22:05.578  INFO 54336 --- [http-nio-8081-exec-6] c.p.s.impl.QiniuStorageServiceImpl       : 生成上传凭证成功，文件名: phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e
2025-05-27 15:22:05.579  INFO 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表成功加载七牛云配置: configId=5, configName=七牛云
2025-05-27 15:22:05.579 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 从文件上传配置表加载七牛云配置
2025-05-27 15:22:05.579 DEBUG 54336 --- [http-nio-8081-exec-6] com.phototagmoment.config.QiniuConfig    : 七牛云配置刷新完成: enabled=true, bucket=photo-tag-test, domain=http://sw5eg63qc.hn-bkt.clouddn.com
2025-05-27 15:22:05.580  INFO 54336 --- [http-nio-8081-exec-6] c.p.s.impl.QiniuStorageServiceImpl       : 生成上传凭证成功，文件名: phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375
2025-05-27 15:22:05.581  INFO 54336 --- [http-nio-8081-exec-6] c.p.controller.PhotoUploadController     : 获取批量上传凭证成功，数量: 9
2025-05-27 15:22:05.582 DEBUG 54336 --- [http-nio-8081-exec-6] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:05.583 DEBUG 54336 --- [http-nio-8081-exec-6] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data={uploadUrl=https://upload-z2.qiniup.com, keys={key1=photota (truncated)...]
2025-05-27 15:22:05.590 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:05.590 DEBUG 54336 --- [http-nio-8081-exec-6] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:06.964 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /photo/save-info
2025-05-27 15:22:06.965 DEBUG 54336 --- [http-nio-8081-exec-7] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:06.965  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:06.965 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/photo/save-info
2025-05-27 15:22:06.965  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:06.966  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:06.966  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:06.966  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:06.966  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:06.966  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/upload
2025-05-27 15:22:06.966  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:06.966  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:06.966  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:06.966  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:06.966  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:06.968  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:06.968  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:06.968  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:06.968  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:06.968  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:06.968  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:06.968  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:06.968  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:06.969  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   content-length = 709
2025-05-27 15:22:06.969  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:06.969  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:06.969  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:06.969  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:06.969  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:06.969  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/save-info
2025-05-27 15:22:06.971  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:06.972 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:06.972  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:06.973 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:06.973 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:06.974 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:06.975  WARN 54336 --- [http-nio-8081-exec-7] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:06.975  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:06.975  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:06.975  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:06.976 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /photo/save-info] with attributes [authenticated]
2025-05-27 15:22:06.976 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /photo/save-info
2025-05-27 15:22:06.976  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:06.976  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/photo/save-info
2025-05-27 15:22:06.976 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.web.servlet.DispatcherServlet        : POST "/api/photo/save-info", parameters={}
2025-05-27 15:22:06.977 DEBUG 54336 --- [http-nio-8081-exec-7] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#savePhotoInfo(PhotoUploadWithMentionsDTO)
2025-05-27 15:22:07.021 DEBUG 54336 --- [http-nio-8081-exec-7] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [PhotoUploadWithMentionsDTO(title=PhotoTag照片笔记成都, description=<span class="topic-tag" data-tag-name=" (truncated)...]
2025-05-27 15:22:07.151 DEBUG 54336 --- [http-nio-8081-exec-7] horizationManagerBeforeMethodInterceptor : Authorizing method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:07.152 DEBUG 54336 --- [http-nio-8081-exec-7] horizationManagerBeforeMethodInterceptor : Authorized method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:07.175 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:07.176 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:07.176 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:07.178 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:07.179  WARN 54336 --- [http-nio-8081-exec-7] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:07.179 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:07.191 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.195 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.PhotoMapper.insert            : ==>  Preparing: INSERT INTO ptm_photo ( user_id, group_id, title, description, url, thumbnail_url, storage_path, original_filename, file_type, width, height, location, visibility, allow_comment, allow_download, like_count, comment_count, collect_count, view_count, status, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:07.200 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.PhotoMapper.insert            : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910(String), 1.jpg(String), image/jpeg(String), 2048(Integer), 1586(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2025-05-27T15:22:07.191293(LocalDateTime), 2025-05-27T15:22:07.195657300(LocalDateTime)
2025-05-27 15:22:07.209 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.PhotoMapper.insert            : <==    Updates: 1
2025-05-27 15:22:07.217 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.218 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.PhotoTagMapper.batchInsert    : ==>  Preparing: INSERT INTO ptm_photo_tag (photo_id, tag_name) VALUES (?, ?)
2025-05-27 15:22:07.219 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.PhotoTagMapper.batchInsert    : ==> Parameters: 78(Long), PhotoTag照片笔记(String)
2025-05-27 15:22:07.220 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.PhotoTagMapper.batchInsert    : <==    Updates: 1
2025-05-27 15:22:07.237  INFO 54336 --- [http-nio-8081-exec-7] c.p.service.impl.PhotoServiceImpl        : 照片已提交自动审核，照片ID: 78
2025-05-27 15:22:07.239 DEBUG 54336 --- [http-nio-8081-exec-7] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:07.239 DEBUG 54336 --- [http-nio-8081-exec-7] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=78, timestamp=1748330527238)]
2025-05-27 15:22:07.240  INFO 54336 --- [audit-task-1] com.phototagmoment.task.PhotoAuditTask   : 开始自动审核照片，照片ID: 78
2025-05-27 15:22:07.241 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:07.242 DEBUG 54336 --- [http-nio-8081-exec-7] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:07.254 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /photo/save-info
2025-05-27 15:22:07.254 DEBUG 54336 --- [http-nio-8081-exec-8] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:07.254  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:07.254 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/photo/save-info
2025-05-27 15:22:07.255  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:07.255  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:07.255  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.255  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:07.255  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:07.256  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/upload
2025-05-27 15:22:07.256  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:07.256  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:07.256  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:07.256  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:07.256  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.256  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:07.257  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:07.257  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:07.257  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:07.257  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:07.257  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:07.257  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.257  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:07.258  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   content-length = 739
2025-05-27 15:22:07.258  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:07.258  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:07.258  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.258  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.258  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:07.258  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/save-info
2025-05-27 15:22:07.260  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:07.264 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:07.265  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:07.266 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:07.267 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:07.268 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:07.269  WARN 54336 --- [http-nio-8081-exec-8] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:07.269  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:07.270  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:07.270  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:07.270 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /photo/save-info] with attributes [authenticated]
2025-05-27 15:22:07.271 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /photo/save-info
2025-05-27 15:22:07.271  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:07.271  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/photo/save-info
2025-05-27 15:22:07.271 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.web.servlet.DispatcherServlet        : POST "/api/photo/save-info", parameters={}
2025-05-27 15:22:07.272 DEBUG 54336 --- [http-nio-8081-exec-8] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#savePhotoInfo(PhotoUploadWithMentionsDTO)
2025-05-27 15:22:07.273 DEBUG 54336 --- [http-nio-8081-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [PhotoUploadWithMentionsDTO(title=PhotoTag照片笔记成都, description=<span class="topic-tag" data-tag-name=" (truncated)...]
2025-05-27 15:22:07.274 DEBUG 54336 --- [http-nio-8081-exec-8] horizationManagerBeforeMethodInterceptor : Authorizing method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:07.274 DEBUG 54336 --- [http-nio-8081-exec-8] horizationManagerBeforeMethodInterceptor : Authorized method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:07.275 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:07.275 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:07.276 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:07.277 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:07.279  WARN 54336 --- [http-nio-8081-exec-8] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:07.279 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:07.281 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.283 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.PhotoMapper.insert            : ==>  Preparing: INSERT INTO ptm_photo ( user_id, group_id, title, description, url, thumbnail_url, storage_path, original_filename, file_type, width, height, location, visibility, allow_comment, allow_download, like_count, comment_count, collect_count, view_count, status, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:07.285 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.PhotoMapper.insert            : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63(String), 3f206ae6bce75e0bdf5a32cb7e8a09c.jpg(String), image/jpeg(String), 1080(Integer), 1620(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2025-05-27T15:22:07.282298700(LocalDateTime), 2025-05-27T15:22:07.282298700(LocalDateTime)
2025-05-27 15:22:07.287 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.PhotoMapper.insert            : <==    Updates: 1
2025-05-27 15:22:07.287 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.288 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.PhotoTagMapper.batchInsert    : ==>  Preparing: INSERT INTO ptm_photo_tag (photo_id, tag_name) VALUES (?, ?)
2025-05-27 15:22:07.288 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.PhotoTagMapper.batchInsert    : ==> Parameters: 79(Long), PhotoTag照片笔记(String)
2025-05-27 15:22:07.290 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.PhotoTagMapper.batchInsert    : <==    Updates: 1
2025-05-27 15:22:07.295  INFO 54336 --- [http-nio-8081-exec-8] c.p.service.impl.PhotoServiceImpl        : 照片已提交自动审核，照片ID: 79
2025-05-27 15:22:07.295  INFO 54336 --- [audit-task-2] com.phototagmoment.task.PhotoAuditTask   : 开始自动审核照片，照片ID: 79
2025-05-27 15:22:07.297 DEBUG 54336 --- [http-nio-8081-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:07.297 DEBUG 54336 --- [http-nio-8081-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=79, timestamp=1748330527296)]
2025-05-27 15:22:07.298 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:07.299 DEBUG 54336 --- [http-nio-8081-exec-8] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:07.313 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,title,description,url,thumbnail_url,storage_path,file_type,width,height,location,visibility,allow_comment,allow_download,like_count,comment_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (id = ?)
2025-05-27 15:22:07.314 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,title,description,url,thumbnail_url,storage_path,file_type,width,height,location,visibility,allow_comment,allow_download,like_count,comment_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (id = ?)
2025-05-27 15:22:07.314 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 79(Long)
2025-05-27 15:22:07.314 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 78(Long)
2025-05-27 15:22:07.317 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectList        : <==      Total: 1
2025-05-27 15:22:07.317 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectList        : <==      Total: 1
2025-05-27 15:22:07.317 DEBUG 54336 --- [audit-task-2] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:22:07.317 DEBUG 54336 --- [audit-task-1] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:22:07.317 DEBUG 54336 --- [audit-task-1] c.p.m.S.getConfigValueByKey              : ==> Parameters: sensitive.word.filter(String)
2025-05-27 15:22:07.317 DEBUG 54336 --- [audit-task-2] c.p.m.S.getConfigValueByKey              : ==> Parameters: sensitive.word.filter(String)
2025-05-27 15:22:07.318 DEBUG 54336 --- [audit-task-2] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:22:07.318 DEBUG 54336 --- [audit-task-1] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:22:07.326 DEBUG 54336 --- [audit-task-2] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:22:07.326 DEBUG 54336 --- [audit-task-1] c.p.m.S.getConfigValueByKey              : ==>  Preparing: SELECT config_value FROM ptm_system_config WHERE config_key = ?
2025-05-27 15:22:07.327 DEBUG 54336 --- [audit-task-2] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.contact-info.filter(String)
2025-05-27 15:22:07.327 DEBUG 54336 --- [audit-task-1] c.p.m.S.getConfigValueByKey              : ==> Parameters: content-moderation.contact-info.filter(String)
2025-05-27 15:22:07.328 DEBUG 54336 --- [audit-task-2] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:22:07.328 DEBUG 54336 --- [audit-task-1] c.p.m.S.getConfigValueByKey              : <==      Total: 1
2025-05-27 15:22:07.329  INFO 54336 --- [audit-task-2] com.phototagmoment.task.PhotoAuditTask   : 文本内容审核不通过，照片ID: 79, 原因: 文本包含微信号
2025-05-27 15:22:07.329  INFO 54336 --- [audit-task-1] com.phototagmoment.task.PhotoAuditTask   : 文本内容审核不通过，照片ID: 78, 原因: 文本包含微信号
2025-05-27 15:22:07.331 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectById        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE id=? AND is_deleted=0
2025-05-27 15:22:07.331 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectById        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE id=? AND is_deleted=0
2025-05-27 15:22:07.332 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectById        : ==> Parameters: 79(Long)
2025-05-27 15:22:07.332 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectById        : ==> Parameters: 78(Long)
2025-05-27 15:22:07.334 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectById        : <==      Total: 1
2025-05-27 15:22:07.334 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectById        : <==      Total: 1
2025-05-27 15:22:07.337 DEBUG 54336 --- [audit-task-1] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.337 DEBUG 54336 --- [audit-task-2] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.337 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoAuditMapper.insert       : ==>  Preparing: INSERT INTO ptm_photo_audit ( photo_id, audit_type, audit_result, reject_reason, audit_time, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:07.337 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoAuditMapper.insert       : ==>  Preparing: INSERT INTO ptm_photo_audit ( photo_id, audit_type, audit_result, reject_reason, audit_time, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:07.339 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoAuditMapper.insert       : ==> Parameters: 78(Long), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:07.335801400(LocalDateTime), 2025-05-27T15:22:07.337808(LocalDateTime), 2025-05-27T15:22:07.337808(LocalDateTime)
2025-05-27 15:22:07.339 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoAuditMapper.insert       : ==> Parameters: 79(Long), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:07.335801400(LocalDateTime), 2025-05-27T15:22:07.337808(LocalDateTime), 2025-05-27T15:22:07.337808(LocalDateTime)
2025-05-27 15:22:07.340 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoAuditMapper.insert       : <==    Updates: 1
2025-05-27 15:22:07.340 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoAuditMapper.insert       : <==    Updates: 1
2025-05-27 15:22:07.352 DEBUG 54336 --- [audit-task-1] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充更新时间
2025-05-27 15:22:07.352 DEBUG 54336 --- [audit-task-2] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充更新时间
2025-05-27 15:22:07.370 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.updateById        : ==>  Preparing: UPDATE ptm_photo SET user_id=?, group_id=?, title=?, description=?, url=?, thumbnail_url=?, storage_path=?, original_filename=?, file_type=?, width=?, height=?, location=?, visibility=?, allow_comment=?, allow_download=?, like_count=?, comment_count=?, collect_count=?, view_count=?, status=?, reject_reason=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-05-27 15:22:07.370 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.updateById        : ==>  Preparing: UPDATE ptm_photo SET user_id=?, group_id=?, title=?, description=?, url=?, thumbnail_url=?, storage_path=?, original_filename=?, file_type=?, width=?, height=?, location=?, visibility=?, allow_comment=?, allow_download=?, like_count=?, comment_count=?, collect_count=?, view_count=?, status=?, reject_reason=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-05-27 15:22:07.371 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.updateById        : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910(String), 1.jpg(String), image/jpeg(String), 2048(Integer), 1586(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:07(LocalDateTime), 2025-05-27T15:22:07(LocalDateTime), 78(Long)
2025-05-27 15:22:07.371 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.updateById        : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63(String), 3f206ae6bce75e0bdf5a32cb7e8a09c.jpg(String), image/jpeg(String), 1080(Integer), 1620(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:07(LocalDateTime), 2025-05-27T15:22:07(LocalDateTime), 79(Long)
2025-05-27 15:22:07.373 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.updateById        : <==    Updates: 1
2025-05-27 15:22:07.373 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.updateById        : <==    Updates: 1
2025-05-27 15:22:07.377  INFO 54336 --- [audit-task-2] com.phototagmoment.task.PhotoAuditTask   : 自动审核不通过，照片ID: 79, 原因: 文本包含微信号
2025-05-27 15:22:07.377  INFO 54336 --- [audit-task-1] com.phototagmoment.task.PhotoAuditTask   : 自动审核不通过，照片ID: 78, 原因: 文本包含微信号
2025-05-27 15:22:07.389 DEBUG 54336 --- [audit-task-1] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.389 DEBUG 54336 --- [audit-task-2] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.389 DEBUG 54336 --- [audit-task-1] c.p.mapper.NotificationMapper.insert     : ==>  Preparing: INSERT INTO ptm_notification ( user_id, type, content, is_read, created_at ) VALUES ( ?, ?, ?, ?, ? )
2025-05-27 15:22:07.389 DEBUG 54336 --- [audit-task-2] c.p.mapper.NotificationMapper.insert     : ==>  Preparing: INSERT INTO ptm_notification ( user_id, type, content, is_read, created_at ) VALUES ( ?, ?, ?, ?, ? )
2025-05-27 15:22:07.390 DEBUG 54336 --- [audit-task-1] c.p.mapper.NotificationMapper.insert     : ==> Parameters: 1(Long), 5(Integer), 您的照片《PhotoTag照片笔记成都》未通过审核，原因：文本包含微信号(String), 0(Integer), 2025-05-27T15:22:07.389139200(LocalDateTime)
2025-05-27 15:22:07.390 DEBUG 54336 --- [audit-task-2] c.p.mapper.NotificationMapper.insert     : ==> Parameters: 1(Long), 5(Integer), 您的照片《PhotoTag照片笔记成都》未通过审核，原因：文本包含微信号(String), 0(Integer), 2025-05-27T15:22:07.389139200(LocalDateTime)
2025-05-27 15:22:07.391 DEBUG 54336 --- [audit-task-2] c.p.mapper.NotificationMapper.insert     : <==    Updates: 1
2025-05-27 15:22:07.391 DEBUG 54336 --- [audit-task-1] c.p.mapper.NotificationMapper.insert     : <==    Updates: 1
2025-05-27 15:22:07.620 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /photo/save-info
2025-05-27 15:22:07.620 DEBUG 54336 --- [http-nio-8081-exec-10] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:07.620  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:07.621 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/photo/save-info
2025-05-27 15:22:07.621  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:07.621  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:07.621  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.621  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:07.622  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:07.622  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/upload
2025-05-27 15:22:07.622  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:07.622  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:07.622  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:07.622  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:07.623  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.623  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:07.623  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:07.623  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:07.623  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:07.623  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:07.623  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:07.624  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.624  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:07.624  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   content-length = 739
2025-05-27 15:22:07.624  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:07.624  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:07.624  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.625  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.625  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:07.625  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/save-info
2025-05-27 15:22:07.630  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:07.636 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:07.636  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:07.637 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:07.637 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:07.638 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:07.639  WARN 54336 --- [http-nio-8081-exec-10] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:07.639  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:07.640  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:07.640  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:07.640 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /photo/save-info] with attributes [authenticated]
2025-05-27 15:22:07.641 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /photo/save-info
2025-05-27 15:22:07.641  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:07.641  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/photo/save-info
2025-05-27 15:22:07.641 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.web.servlet.DispatcherServlet        : POST "/api/photo/save-info", parameters={}
2025-05-27 15:22:07.642 DEBUG 54336 --- [http-nio-8081-exec-10] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#savePhotoInfo(PhotoUploadWithMentionsDTO)
2025-05-27 15:22:07.643 DEBUG 54336 --- [http-nio-8081-exec-10] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [PhotoUploadWithMentionsDTO(title=PhotoTag照片笔记成都, description=<span class="topic-tag" data-tag-name=" (truncated)...]
2025-05-27 15:22:07.644 DEBUG 54336 --- [http-nio-8081-exec-10] horizationManagerBeforeMethodInterceptor : Authorizing method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:07.644 DEBUG 54336 --- [http-nio-8081-exec-10] horizationManagerBeforeMethodInterceptor : Authorized method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:07.646 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:07.648 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:07.648 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:07.652 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:07.653  WARN 54336 --- [http-nio-8081-exec-10] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:07.653 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:07.655 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.655 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.PhotoMapper.insert            : ==>  Preparing: INSERT INTO ptm_photo ( user_id, group_id, title, description, url, thumbnail_url, storage_path, original_filename, file_type, width, height, location, visibility, allow_comment, allow_download, like_count, comment_count, collect_count, view_count, status, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:07.656 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.PhotoMapper.insert            : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6(String), 12ff1da46d994e45a968ea1dff88caa.jpg(String), image/jpeg(String), 1080(Integer), 1620(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2025-05-27T15:22:07.655384100(LocalDateTime), 2025-05-27T15:22:07.655384100(LocalDateTime)
2025-05-27 15:22:07.658 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.PhotoMapper.insert            : <==    Updates: 1
2025-05-27 15:22:07.660 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.661 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.PhotoTagMapper.batchInsert    : ==>  Preparing: INSERT INTO ptm_photo_tag (photo_id, tag_name) VALUES (?, ?)
2025-05-27 15:22:07.662 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.PhotoTagMapper.batchInsert    : ==> Parameters: 80(Long), PhotoTag照片笔记(String)
2025-05-27 15:22:07.665 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.PhotoTagMapper.batchInsert    : <==    Updates: 1
2025-05-27 15:22:07.670  INFO 54336 --- [http-nio-8081-exec-10] c.p.service.impl.PhotoServiceImpl        : 照片已提交自动审核，照片ID: 80
2025-05-27 15:22:07.670  INFO 54336 --- [audit-task-3] com.phototagmoment.task.PhotoAuditTask   : 开始自动审核照片，照片ID: 80
2025-05-27 15:22:07.671 DEBUG 54336 --- [http-nio-8081-exec-10] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:07.671 DEBUG 54336 --- [http-nio-8081-exec-10] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=80, timestamp=1748330527670)]
2025-05-27 15:22:07.672 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,title,description,url,thumbnail_url,storage_path,file_type,width,height,location,visibility,allow_comment,allow_download,like_count,comment_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (id = ?)
2025-05-27 15:22:07.672 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:07.672 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 80(Long)
2025-05-27 15:22:07.672 DEBUG 54336 --- [http-nio-8081-exec-10] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:07.675 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectList        : <==      Total: 1
2025-05-27 15:22:07.676  INFO 54336 --- [audit-task-3] com.phototagmoment.task.PhotoAuditTask   : 文本内容审核不通过，照片ID: 80, 原因: 文本包含微信号
2025-05-27 15:22:07.678 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectById        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE id=? AND is_deleted=0
2025-05-27 15:22:07.680 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectById        : ==> Parameters: 80(Long)
2025-05-27 15:22:07.685 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectById        : <==      Total: 1
2025-05-27 15:22:07.686 DEBUG 54336 --- [audit-task-3] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.687 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoAuditMapper.insert       : ==>  Preparing: INSERT INTO ptm_photo_audit ( photo_id, audit_type, audit_result, reject_reason, audit_time, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:07.688 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoAuditMapper.insert       : ==> Parameters: 80(Long), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:07.685376(LocalDateTime), 2025-05-27T15:22:07.686377800(LocalDateTime), 2025-05-27T15:22:07.686377800(LocalDateTime)
2025-05-27 15:22:07.689 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoAuditMapper.insert       : <==    Updates: 1
2025-05-27 15:22:07.691 DEBUG 54336 --- [audit-task-3] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充更新时间
2025-05-27 15:22:07.692 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.updateById        : ==>  Preparing: UPDATE ptm_photo SET user_id=?, group_id=?, title=?, description=?, url=?, thumbnail_url=?, storage_path=?, original_filename=?, file_type=?, width=?, height=?, location=?, visibility=?, allow_comment=?, allow_download=?, like_count=?, comment_count=?, collect_count=?, view_count=?, status=?, reject_reason=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-05-27 15:22:07.694 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.updateById        : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6(String), 12ff1da46d994e45a968ea1dff88caa.jpg(String), image/jpeg(String), 1080(Integer), 1620(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:08(LocalDateTime), 2025-05-27T15:22:08(LocalDateTime), 80(Long)
2025-05-27 15:22:07.698 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.updateById        : <==    Updates: 1
2025-05-27 15:22:07.705  INFO 54336 --- [audit-task-3] com.phototagmoment.task.PhotoAuditTask   : 自动审核不通过，照片ID: 80, 原因: 文本包含微信号
2025-05-27 15:22:07.707 DEBUG 54336 --- [audit-task-3] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.708 DEBUG 54336 --- [audit-task-3] c.p.mapper.NotificationMapper.insert     : ==>  Preparing: INSERT INTO ptm_notification ( user_id, type, content, is_read, created_at ) VALUES ( ?, ?, ?, ?, ? )
2025-05-27 15:22:07.709 DEBUG 54336 --- [audit-task-3] c.p.mapper.NotificationMapper.insert     : ==> Parameters: 1(Long), 5(Integer), 您的照片《PhotoTag照片笔记成都》未通过审核，原因：文本包含微信号(String), 0(Integer), 2025-05-27T15:22:07.707545300(LocalDateTime)
2025-05-27 15:22:07.710 DEBUG 54336 --- [audit-task-3] c.p.mapper.NotificationMapper.insert     : <==    Updates: 1
2025-05-27 15:22:07.885 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /photo/save-info
2025-05-27 15:22:07.885 DEBUG 54336 --- [http-nio-8081-exec-9] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:07.886  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:07.886 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/photo/save-info
2025-05-27 15:22:07.886  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:07.886  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:07.886  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.886  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:07.887  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:07.887  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/upload
2025-05-27 15:22:07.887  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:07.887  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:07.887  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:07.887  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:07.887  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.887  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:07.887  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:07.887  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:07.888  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:07.888  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:07.888  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:07.888  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.888  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:07.888  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   content-length = 739
2025-05-27 15:22:07.888  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:07.889  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:07.889  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.889  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.889  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:07.890  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/save-info
2025-05-27 15:22:07.892  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:07.894 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:07.895  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:07.896 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:07.897 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:07.899 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:07.900  WARN 54336 --- [http-nio-8081-exec-9] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:07.900  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:07.900  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:07.900  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:07.901 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /photo/save-info] with attributes [authenticated]
2025-05-27 15:22:07.901 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /photo/save-info
2025-05-27 15:22:07.901  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:07.901  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/photo/save-info
2025-05-27 15:22:07.902 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.web.servlet.DispatcherServlet        : POST "/api/photo/save-info", parameters={}
2025-05-27 15:22:07.903 DEBUG 54336 --- [http-nio-8081-exec-9] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#savePhotoInfo(PhotoUploadWithMentionsDTO)
2025-05-27 15:22:07.904 DEBUG 54336 --- [http-nio-8081-exec-9] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [PhotoUploadWithMentionsDTO(title=PhotoTag照片笔记成都, description=<span class="topic-tag" data-tag-name=" (truncated)...]
2025-05-27 15:22:07.904 DEBUG 54336 --- [http-nio-8081-exec-9] horizationManagerBeforeMethodInterceptor : Authorizing method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:07.905 DEBUG 54336 --- [http-nio-8081-exec-9] horizationManagerBeforeMethodInterceptor : Authorized method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:07.906 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:07.906 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:07.907 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:07.913 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:07.914  WARN 54336 --- [http-nio-8081-exec-9] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:07.915 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:07.920 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.920 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.PhotoMapper.insert            : ==>  Preparing: INSERT INTO ptm_photo ( user_id, group_id, title, description, url, thumbnail_url, storage_path, original_filename, file_type, width, height, location, visibility, allow_comment, allow_download, like_count, comment_count, collect_count, view_count, status, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:07.921 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.PhotoMapper.insert            : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7(String), 388dc9c99659cd0aae55e1d8d84c7da.jpg(String), image/jpeg(String), 1080(Integer), 1620(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2025-05-27T15:22:07.920941900(LocalDateTime), 2025-05-27T15:22:07.920941900(LocalDateTime)
2025-05-27 15:22:07.923 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.PhotoMapper.insert            : <==    Updates: 1
2025-05-27 15:22:07.924 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.924 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.PhotoTagMapper.batchInsert    : ==>  Preparing: INSERT INTO ptm_photo_tag (photo_id, tag_name) VALUES (?, ?)
2025-05-27 15:22:07.924 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.PhotoTagMapper.batchInsert    : ==> Parameters: 81(Long), PhotoTag照片笔记(String)
2025-05-27 15:22:07.926 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.PhotoTagMapper.batchInsert    : <==    Updates: 1
2025-05-27 15:22:07.931  INFO 54336 --- [http-nio-8081-exec-9] c.p.service.impl.PhotoServiceImpl        : 照片已提交自动审核，照片ID: 81
2025-05-27 15:22:07.932  INFO 54336 --- [audit-task-4] com.phototagmoment.task.PhotoAuditTask   : 开始自动审核照片，照片ID: 81
2025-05-27 15:22:07.933 DEBUG 54336 --- [http-nio-8081-exec-9] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:07.933 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,title,description,url,thumbnail_url,storage_path,file_type,width,height,location,visibility,allow_comment,allow_download,like_count,comment_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (id = ?)
2025-05-27 15:22:07.933 DEBUG 54336 --- [http-nio-8081-exec-9] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=81, timestamp=1748330527932)]
2025-05-27 15:22:07.934 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 81(Long)
2025-05-27 15:22:07.934 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:07.935 DEBUG 54336 --- [http-nio-8081-exec-9] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:07.935 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectList        : <==      Total: 1
2025-05-27 15:22:07.936  INFO 54336 --- [audit-task-4] com.phototagmoment.task.PhotoAuditTask   : 文本内容审核不通过，照片ID: 81, 原因: 文本包含微信号
2025-05-27 15:22:07.937 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectById        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE id=? AND is_deleted=0
2025-05-27 15:22:07.938 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectById        : ==> Parameters: 81(Long)
2025-05-27 15:22:07.939 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectById        : <==      Total: 1
2025-05-27 15:22:07.940 DEBUG 54336 --- [audit-task-4] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.940 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoAuditMapper.insert       : ==>  Preparing: INSERT INTO ptm_photo_audit ( photo_id, audit_type, audit_result, reject_reason, audit_time, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:07.941 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoAuditMapper.insert       : ==> Parameters: 81(Long), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:07.939973300(LocalDateTime), 2025-05-27T15:22:07.940973300(LocalDateTime), 2025-05-27T15:22:07.940973300(LocalDateTime)
2025-05-27 15:22:07.943 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoAuditMapper.insert       : <==    Updates: 1
2025-05-27 15:22:07.944 DEBUG 54336 --- [audit-task-4] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充更新时间
2025-05-27 15:22:07.948 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.updateById        : ==>  Preparing: UPDATE ptm_photo SET user_id=?, group_id=?, title=?, description=?, url=?, thumbnail_url=?, storage_path=?, original_filename=?, file_type=?, width=?, height=?, location=?, visibility=?, allow_comment=?, allow_download=?, like_count=?, comment_count=?, collect_count=?, view_count=?, status=?, reject_reason=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-05-27 15:22:07.950 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.updateById        : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7(String), 388dc9c99659cd0aae55e1d8d84c7da.jpg(String), image/jpeg(String), 1080(Integer), 1620(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:08(LocalDateTime), 2025-05-27T15:22:08(LocalDateTime), 81(Long)
2025-05-27 15:22:07.952 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.updateById        : <==    Updates: 1
2025-05-27 15:22:07.956  INFO 54336 --- [audit-task-4] com.phototagmoment.task.PhotoAuditTask   : 自动审核不通过，照片ID: 81, 原因: 文本包含微信号
2025-05-27 15:22:07.958 DEBUG 54336 --- [audit-task-4] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:07.959 DEBUG 54336 --- [audit-task-4] c.p.mapper.NotificationMapper.insert     : ==>  Preparing: INSERT INTO ptm_notification ( user_id, type, content, is_read, created_at ) VALUES ( ?, ?, ?, ?, ? )
2025-05-27 15:22:07.959 DEBUG 54336 --- [audit-task-4] c.p.mapper.NotificationMapper.insert     : ==> Parameters: 1(Long), 5(Integer), 您的照片《PhotoTag照片笔记成都》未通过审核，原因：文本包含微信号(String), 0(Integer), 2025-05-27T15:22:07.959005800(LocalDateTime)
2025-05-27 15:22:07.960 DEBUG 54336 --- [audit-task-4] c.p.mapper.NotificationMapper.insert     : <==    Updates: 1
2025-05-27 15:22:07.989 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /photo/save-info
2025-05-27 15:22:07.989 DEBUG 54336 --- [http-nio-8081-exec-3] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:07.989  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:07.989 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/photo/save-info
2025-05-27 15:22:07.989  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:07.989  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:07.990  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.990  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:07.990  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:07.990  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/upload
2025-05-27 15:22:07.990  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:07.990  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:07.990  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:07.991  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:07.991  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.991  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:07.991  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:07.991  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:07.991  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:07.991  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:07.991  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:07.991  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.992  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:07.992  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   content-length = 739
2025-05-27 15:22:07.992  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:07.992  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:07.992  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.992  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:07.992  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:07.992  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/save-info
2025-05-27 15:22:07.994  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:07.997 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:07.998  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:07.999 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:07.999 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:08.003 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:08.004  WARN 54336 --- [http-nio-8081-exec-3] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:08.004  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:08.004  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:08.004  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:08.005 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /photo/save-info] with attributes [authenticated]
2025-05-27 15:22:08.005 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /photo/save-info
2025-05-27 15:22:08.005  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:08.005  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/photo/save-info
2025-05-27 15:22:08.005 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : POST "/api/photo/save-info", parameters={}
2025-05-27 15:22:08.006 DEBUG 54336 --- [http-nio-8081-exec-3] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#savePhotoInfo(PhotoUploadWithMentionsDTO)
2025-05-27 15:22:08.007 DEBUG 54336 --- [http-nio-8081-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [PhotoUploadWithMentionsDTO(title=PhotoTag照片笔记成都, description=<span class="topic-tag" data-tag-name=" (truncated)...]
2025-05-27 15:22:08.007 DEBUG 54336 --- [http-nio-8081-exec-3] horizationManagerBeforeMethodInterceptor : Authorizing method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:08.007 DEBUG 54336 --- [http-nio-8081-exec-3] horizationManagerBeforeMethodInterceptor : Authorized method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:08.008 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:08.008 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:08.009 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:08.011 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:08.012  WARN 54336 --- [http-nio-8081-exec-3] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:08.012 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:08.013 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.014 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.PhotoMapper.insert            : ==>  Preparing: INSERT INTO ptm_photo ( user_id, group_id, title, description, url, thumbnail_url, storage_path, original_filename, file_type, width, height, location, visibility, allow_comment, allow_download, like_count, comment_count, collect_count, view_count, status, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:08.016 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.PhotoMapper.insert            : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645(String), 451df8743d210292ce73ed46fc14ced.jpg(String), image/jpeg(String), 4096(Integer), 2732(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2025-05-27T15:22:08.014624300(LocalDateTime), 2025-05-27T15:22:08.014624300(LocalDateTime)
2025-05-27 15:22:08.019 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.PhotoMapper.insert            : <==    Updates: 1
2025-05-27 15:22:08.020 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.021 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.PhotoTagMapper.batchInsert    : ==>  Preparing: INSERT INTO ptm_photo_tag (photo_id, tag_name) VALUES (?, ?)
2025-05-27 15:22:08.021 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.PhotoTagMapper.batchInsert    : ==> Parameters: 82(Long), PhotoTag照片笔记(String)
2025-05-27 15:22:08.022 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.PhotoTagMapper.batchInsert    : <==    Updates: 1
2025-05-27 15:22:08.027  INFO 54336 --- [http-nio-8081-exec-3] c.p.service.impl.PhotoServiceImpl        : 照片已提交自动审核，照片ID: 82
2025-05-27 15:22:08.027  INFO 54336 --- [audit-task-5] com.phototagmoment.task.PhotoAuditTask   : 开始自动审核照片，照片ID: 82
2025-05-27 15:22:08.027 DEBUG 54336 --- [http-nio-8081-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:08.028 DEBUG 54336 --- [http-nio-8081-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=82, timestamp=1748330528027)]
2025-05-27 15:22:08.028 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,title,description,url,thumbnail_url,storage_path,file_type,width,height,location,visibility,allow_comment,allow_download,like_count,comment_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (id = ?)
2025-05-27 15:22:08.029 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 82(Long)
2025-05-27 15:22:08.029 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:08.030 DEBUG 54336 --- [http-nio-8081-exec-3] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:08.033 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoMapper.selectList        : <==      Total: 1
2025-05-27 15:22:08.034  INFO 54336 --- [audit-task-5] com.phototagmoment.task.PhotoAuditTask   : 文本内容审核不通过，照片ID: 82, 原因: 文本包含微信号
2025-05-27 15:22:08.036 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoMapper.selectById        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE id=? AND is_deleted=0
2025-05-27 15:22:08.036 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoMapper.selectById        : ==> Parameters: 82(Long)
2025-05-27 15:22:08.038 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoMapper.selectById        : <==      Total: 1
2025-05-27 15:22:08.039 DEBUG 54336 --- [audit-task-5] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.040 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoAuditMapper.insert       : ==>  Preparing: INSERT INTO ptm_photo_audit ( photo_id, audit_type, audit_result, reject_reason, audit_time, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:08.040 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoAuditMapper.insert       : ==> Parameters: 82(Long), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:08.039167900(LocalDateTime), 2025-05-27T15:22:08.039167900(LocalDateTime), 2025-05-27T15:22:08.039167900(LocalDateTime)
2025-05-27 15:22:08.041 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoAuditMapper.insert       : <==    Updates: 1
2025-05-27 15:22:08.042 DEBUG 54336 --- [audit-task-5] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充更新时间
2025-05-27 15:22:08.044 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoMapper.updateById        : ==>  Preparing: UPDATE ptm_photo SET user_id=?, group_id=?, title=?, description=?, url=?, thumbnail_url=?, storage_path=?, original_filename=?, file_type=?, width=?, height=?, location=?, visibility=?, allow_comment=?, allow_download=?, like_count=?, comment_count=?, collect_count=?, view_count=?, status=?, reject_reason=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-05-27 15:22:08.045 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoMapper.updateById        : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645(String), 451df8743d210292ce73ed46fc14ced.jpg(String), image/jpeg(String), 4096(Integer), 2732(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:08(LocalDateTime), 2025-05-27T15:22:08(LocalDateTime), 82(Long)
2025-05-27 15:22:08.050 DEBUG 54336 --- [audit-task-5] c.p.mapper.PhotoMapper.updateById        : <==    Updates: 1
2025-05-27 15:22:08.054  INFO 54336 --- [audit-task-5] com.phototagmoment.task.PhotoAuditTask   : 自动审核不通过，照片ID: 82, 原因: 文本包含微信号
2025-05-27 15:22:08.055 DEBUG 54336 --- [audit-task-5] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.055 DEBUG 54336 --- [audit-task-5] c.p.mapper.NotificationMapper.insert     : ==>  Preparing: INSERT INTO ptm_notification ( user_id, type, content, is_read, created_at ) VALUES ( ?, ?, ?, ?, ? )
2025-05-27 15:22:08.056 DEBUG 54336 --- [audit-task-5] c.p.mapper.NotificationMapper.insert     : ==> Parameters: 1(Long), 5(Integer), 您的照片《PhotoTag照片笔记成都》未通过审核，原因：文本包含微信号(String), 0(Integer), 2025-05-27T15:22:08.055250300(LocalDateTime)
2025-05-27 15:22:08.057 DEBUG 54336 --- [audit-task-5] c.p.mapper.NotificationMapper.insert     : <==    Updates: 1
2025-05-27 15:22:08.253 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /photo/save-info
2025-05-27 15:22:08.254 DEBUG 54336 --- [http-nio-8081-exec-1] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:08.254  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:08.254 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/photo/save-info
2025-05-27 15:22:08.254  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:08.254  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:08.255  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.255  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:08.255  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:08.255  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/upload
2025-05-27 15:22:08.255  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:08.255  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:08.255  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:08.255  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:08.256  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.256  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:08.256  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:08.256  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:08.256  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:08.256  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:08.256  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:08.256  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.257  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:08.257  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   content-length = 739
2025-05-27 15:22:08.257  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:08.257  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:08.257  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.257  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.257  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:08.257  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/save-info
2025-05-27 15:22:08.259  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:08.263 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:08.263  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:08.264 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:08.264 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:08.267 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:08.267  WARN 54336 --- [http-nio-8081-exec-1] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:08.268  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:08.268  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:08.268  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:08.269 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /photo/save-info] with attributes [authenticated]
2025-05-27 15:22:08.269 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /photo/save-info
2025-05-27 15:22:08.269  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:08.269  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/photo/save-info
2025-05-27 15:22:08.269 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : POST "/api/photo/save-info", parameters={}
2025-05-27 15:22:08.270 DEBUG 54336 --- [http-nio-8081-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#savePhotoInfo(PhotoUploadWithMentionsDTO)
2025-05-27 15:22:08.271 DEBUG 54336 --- [http-nio-8081-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [PhotoUploadWithMentionsDTO(title=PhotoTag照片笔记成都, description=<span class="topic-tag" data-tag-name=" (truncated)...]
2025-05-27 15:22:08.271 DEBUG 54336 --- [http-nio-8081-exec-1] horizationManagerBeforeMethodInterceptor : Authorizing method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:08.271 DEBUG 54336 --- [http-nio-8081-exec-1] horizationManagerBeforeMethodInterceptor : Authorized method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:08.272 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:08.272 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:08.273 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:08.274 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:08.275  WARN 54336 --- [http-nio-8081-exec-1] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:08.275 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:08.276 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.277 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.PhotoMapper.insert            : ==>  Preparing: INSERT INTO ptm_photo ( user_id, group_id, title, description, url, thumbnail_url, storage_path, original_filename, file_type, width, height, location, visibility, allow_comment, allow_download, like_count, comment_count, collect_count, view_count, status, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:08.279 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.PhotoMapper.insert            : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38(String), a8f434cdf357bfaad93afe88cb3a0cc.jpg(String), image/jpeg(String), 4433(Integer), 3072(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2025-05-27T15:22:08.277582(LocalDateTime), 2025-05-27T15:22:08.277582(LocalDateTime)
2025-05-27 15:22:08.283 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.PhotoMapper.insert            : <==    Updates: 1
2025-05-27 15:22:08.285 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.286 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.PhotoTagMapper.batchInsert    : ==>  Preparing: INSERT INTO ptm_photo_tag (photo_id, tag_name) VALUES (?, ?)
2025-05-27 15:22:08.286 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.PhotoTagMapper.batchInsert    : ==> Parameters: 83(Long), PhotoTag照片笔记(String)
2025-05-27 15:22:08.289 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.PhotoTagMapper.batchInsert    : <==    Updates: 1
2025-05-27 15:22:08.294  INFO 54336 --- [audit-task-2] com.phototagmoment.task.PhotoAuditTask   : 开始自动审核照片，照片ID: 83
2025-05-27 15:22:08.294  INFO 54336 --- [http-nio-8081-exec-1] c.p.service.impl.PhotoServiceImpl        : 照片已提交自动审核，照片ID: 83
2025-05-27 15:22:08.296 DEBUG 54336 --- [http-nio-8081-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:08.297 DEBUG 54336 --- [http-nio-8081-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=83, timestamp=1748330528295)]
2025-05-27 15:22:08.299 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,title,description,url,thumbnail_url,storage_path,file_type,width,height,location,visibility,allow_comment,allow_download,like_count,comment_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (id = ?)
2025-05-27 15:22:08.299 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:08.300 DEBUG 54336 --- [http-nio-8081-exec-1] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:08.300 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 83(Long)
2025-05-27 15:22:08.302 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectList        : <==      Total: 1
2025-05-27 15:22:08.302  INFO 54336 --- [audit-task-2] com.phototagmoment.task.PhotoAuditTask   : 文本内容审核不通过，照片ID: 83, 原因: 文本包含微信号
2025-05-27 15:22:08.305 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectById        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE id=? AND is_deleted=0
2025-05-27 15:22:08.305 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectById        : ==> Parameters: 83(Long)
2025-05-27 15:22:08.307 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.selectById        : <==      Total: 1
2025-05-27 15:22:08.309 DEBUG 54336 --- [audit-task-2] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.310 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoAuditMapper.insert       : ==>  Preparing: INSERT INTO ptm_photo_audit ( photo_id, audit_type, audit_result, reject_reason, audit_time, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:08.311 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoAuditMapper.insert       : ==> Parameters: 83(Long), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:08.307342(LocalDateTime), 2025-05-27T15:22:08.309866900(LocalDateTime), 2025-05-27T15:22:08.309866900(LocalDateTime)
2025-05-27 15:22:08.314 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoAuditMapper.insert       : <==    Updates: 1
2025-05-27 15:22:08.316 DEBUG 54336 --- [audit-task-2] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充更新时间
2025-05-27 15:22:08.318 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.updateById        : ==>  Preparing: UPDATE ptm_photo SET user_id=?, group_id=?, title=?, description=?, url=?, thumbnail_url=?, storage_path=?, original_filename=?, file_type=?, width=?, height=?, location=?, visibility=?, allow_comment=?, allow_download=?, like_count=?, comment_count=?, collect_count=?, view_count=?, status=?, reject_reason=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-05-27 15:22:08.320 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.updateById        : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38(String), a8f434cdf357bfaad93afe88cb3a0cc.jpg(String), image/jpeg(String), 4433(Integer), 3072(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:08(LocalDateTime), 2025-05-27T15:22:08(LocalDateTime), 83(Long)
2025-05-27 15:22:08.322 DEBUG 54336 --- [audit-task-2] c.p.mapper.PhotoMapper.updateById        : <==    Updates: 1
2025-05-27 15:22:08.327  INFO 54336 --- [audit-task-2] com.phototagmoment.task.PhotoAuditTask   : 自动审核不通过，照片ID: 83, 原因: 文本包含微信号
2025-05-27 15:22:08.331 DEBUG 54336 --- [audit-task-2] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.331 DEBUG 54336 --- [audit-task-2] c.p.mapper.NotificationMapper.insert     : ==>  Preparing: INSERT INTO ptm_notification ( user_id, type, content, is_read, created_at ) VALUES ( ?, ?, ?, ?, ? )
2025-05-27 15:22:08.332 DEBUG 54336 --- [audit-task-2] c.p.mapper.NotificationMapper.insert     : ==> Parameters: 1(Long), 5(Integer), 您的照片《PhotoTag照片笔记成都》未通过审核，原因：文本包含微信号(String), 0(Integer), 2025-05-27T15:22:08.331139900(LocalDateTime)
2025-05-27 15:22:08.334 DEBUG 54336 --- [audit-task-2] c.p.mapper.NotificationMapper.insert     : <==    Updates: 1
2025-05-27 15:22:08.345 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /photo/save-info
2025-05-27 15:22:08.346 DEBUG 54336 --- [http-nio-8081-exec-5] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:08.346  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:08.346 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/photo/save-info
2025-05-27 15:22:08.346  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:08.346  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:08.347  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.347  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:08.347  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:08.347  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/upload
2025-05-27 15:22:08.347  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:08.347  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:08.347  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:08.347  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:08.347  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.348  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:08.348  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:08.348  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:08.348  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:08.348  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:08.349  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:08.349  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.349  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:08.349  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   content-length = 739
2025-05-27 15:22:08.349  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:08.349  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:08.350  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.350  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.350  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:08.350  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/save-info
2025-05-27 15:22:08.352  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:08.354 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:08.354  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:08.355 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:08.355 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:08.356 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:08.357  WARN 54336 --- [http-nio-8081-exec-5] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:08.357  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:08.358  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:08.358  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:08.359 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /photo/save-info] with attributes [authenticated]
2025-05-27 15:22:08.359 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /photo/save-info
2025-05-27 15:22:08.359  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:08.359  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/photo/save-info
2025-05-27 15:22:08.360 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.web.servlet.DispatcherServlet        : POST "/api/photo/save-info", parameters={}
2025-05-27 15:22:08.360 DEBUG 54336 --- [http-nio-8081-exec-5] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#savePhotoInfo(PhotoUploadWithMentionsDTO)
2025-05-27 15:22:08.361 DEBUG 54336 --- [http-nio-8081-exec-5] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [PhotoUploadWithMentionsDTO(title=PhotoTag照片笔记成都, description=<span class="topic-tag" data-tag-name=" (truncated)...]
2025-05-27 15:22:08.362 DEBUG 54336 --- [http-nio-8081-exec-5] horizationManagerBeforeMethodInterceptor : Authorizing method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:08.363 DEBUG 54336 --- [http-nio-8081-exec-5] horizationManagerBeforeMethodInterceptor : Authorized method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:08.364 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:08.365 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:08.366 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:08.367 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:08.368  WARN 54336 --- [http-nio-8081-exec-5] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:08.368 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:08.369 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.369 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.PhotoMapper.insert            : ==>  Preparing: INSERT INTO ptm_photo ( user_id, group_id, title, description, url, thumbnail_url, storage_path, original_filename, file_type, width, height, location, visibility, allow_comment, allow_download, like_count, comment_count, collect_count, view_count, status, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:08.370 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.PhotoMapper.insert            : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca(String), b1ba9786b05414db28cdde1746f81b2.jpg(String), image/jpeg(String), 1536(Integer), 1026(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2025-05-27T15:22:08.369772700(LocalDateTime), 2025-05-27T15:22:08.369772700(LocalDateTime)
2025-05-27 15:22:08.372 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.PhotoMapper.insert            : <==    Updates: 1
2025-05-27 15:22:08.373 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.373 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.PhotoTagMapper.batchInsert    : ==>  Preparing: INSERT INTO ptm_photo_tag (photo_id, tag_name) VALUES (?, ?)
2025-05-27 15:22:08.374 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.PhotoTagMapper.batchInsert    : ==> Parameters: 84(Long), PhotoTag照片笔记(String)
2025-05-27 15:22:08.375 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.PhotoTagMapper.batchInsert    : <==    Updates: 1
2025-05-27 15:22:08.383  INFO 54336 --- [http-nio-8081-exec-5] c.p.service.impl.PhotoServiceImpl        : 照片已提交自动审核，照片ID: 84
2025-05-27 15:22:08.383  INFO 54336 --- [audit-task-1] com.phototagmoment.task.PhotoAuditTask   : 开始自动审核照片，照片ID: 84
2025-05-27 15:22:08.385 DEBUG 54336 --- [http-nio-8081-exec-5] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:08.385 DEBUG 54336 --- [http-nio-8081-exec-5] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=84, timestamp=1748330528384)]
2025-05-27 15:22:08.386 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,title,description,url,thumbnail_url,storage_path,file_type,width,height,location,visibility,allow_comment,allow_download,like_count,comment_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (id = ?)
2025-05-27 15:22:08.386 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 84(Long)
2025-05-27 15:22:08.386 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:08.387 DEBUG 54336 --- [http-nio-8081-exec-5] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:08.387 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectList        : <==      Total: 1
2025-05-27 15:22:08.388  INFO 54336 --- [audit-task-1] com.phototagmoment.task.PhotoAuditTask   : 文本内容审核不通过，照片ID: 84, 原因: 文本包含微信号
2025-05-27 15:22:08.389 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectById        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE id=? AND is_deleted=0
2025-05-27 15:22:08.390 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectById        : ==> Parameters: 84(Long)
2025-05-27 15:22:08.391 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.selectById        : <==      Total: 1
2025-05-27 15:22:08.392 DEBUG 54336 --- [audit-task-1] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.392 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoAuditMapper.insert       : ==>  Preparing: INSERT INTO ptm_photo_audit ( photo_id, audit_type, audit_result, reject_reason, audit_time, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:08.393 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoAuditMapper.insert       : ==> Parameters: 84(Long), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:08.392829400(LocalDateTime), 2025-05-27T15:22:08.392829400(LocalDateTime), 2025-05-27T15:22:08.392829400(LocalDateTime)
2025-05-27 15:22:08.394 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoAuditMapper.insert       : <==    Updates: 1
2025-05-27 15:22:08.395 DEBUG 54336 --- [audit-task-1] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充更新时间
2025-05-27 15:22:08.399 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.updateById        : ==>  Preparing: UPDATE ptm_photo SET user_id=?, group_id=?, title=?, description=?, url=?, thumbnail_url=?, storage_path=?, original_filename=?, file_type=?, width=?, height=?, location=?, visibility=?, allow_comment=?, allow_download=?, like_count=?, comment_count=?, collect_count=?, view_count=?, status=?, reject_reason=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-05-27 15:22:08.400 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.updateById        : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca(String), b1ba9786b05414db28cdde1746f81b2.jpg(String), image/jpeg(String), 1536(Integer), 1026(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:08(LocalDateTime), 2025-05-27T15:22:08(LocalDateTime), 84(Long)
2025-05-27 15:22:08.402 DEBUG 54336 --- [audit-task-1] c.p.mapper.PhotoMapper.updateById        : <==    Updates: 1
2025-05-27 15:22:08.406  INFO 54336 --- [audit-task-1] com.phototagmoment.task.PhotoAuditTask   : 自动审核不通过，照片ID: 84, 原因: 文本包含微信号
2025-05-27 15:22:08.407 DEBUG 54336 --- [audit-task-1] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.408 DEBUG 54336 --- [audit-task-1] c.p.mapper.NotificationMapper.insert     : ==>  Preparing: INSERT INTO ptm_notification ( user_id, type, content, is_read, created_at ) VALUES ( ?, ?, ?, ?, ? )
2025-05-27 15:22:08.409 DEBUG 54336 --- [audit-task-1] c.p.mapper.NotificationMapper.insert     : ==> Parameters: 1(Long), 5(Integer), 您的照片《PhotoTag照片笔记成都》未通过审核，原因：文本包含微信号(String), 0(Integer), 2025-05-27T15:22:08.408767700(LocalDateTime)
2025-05-27 15:22:08.410 DEBUG 54336 --- [audit-task-1] c.p.mapper.NotificationMapper.insert     : <==    Updates: 1
2025-05-27 15:22:08.625 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /photo/save-info
2025-05-27 15:22:08.625 DEBUG 54336 --- [http-nio-8081-exec-2] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:08.625  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:08.626 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/photo/save-info
2025-05-27 15:22:08.626  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:08.626  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:08.626  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.626  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:08.626  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:08.626  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/upload
2025-05-27 15:22:08.626  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:08.627  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   content-length = 739
2025-05-27 15:22:08.628  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:08.628  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:08.628  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.628  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.628  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:08.628  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/save-info
2025-05-27 15:22:08.631  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:08.633 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:08.634  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:08.635 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:08.635 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:08.637 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:08.638  WARN 54336 --- [http-nio-8081-exec-2] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:08.638  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:08.638  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:08.638  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:08.639 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /photo/save-info] with attributes [authenticated]
2025-05-27 15:22:08.639 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /photo/save-info
2025-05-27 15:22:08.639  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:08.639  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/photo/save-info
2025-05-27 15:22:08.639 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.web.servlet.DispatcherServlet        : POST "/api/photo/save-info", parameters={}
2025-05-27 15:22:08.640 DEBUG 54336 --- [http-nio-8081-exec-2] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#savePhotoInfo(PhotoUploadWithMentionsDTO)
2025-05-27 15:22:08.641 DEBUG 54336 --- [http-nio-8081-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [PhotoUploadWithMentionsDTO(title=PhotoTag照片笔记成都, description=<span class="topic-tag" data-tag-name=" (truncated)...]
2025-05-27 15:22:08.642 DEBUG 54336 --- [http-nio-8081-exec-2] horizationManagerBeforeMethodInterceptor : Authorizing method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:08.642 DEBUG 54336 --- [http-nio-8081-exec-2] horizationManagerBeforeMethodInterceptor : Authorized method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:08.643 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:08.643 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:08.644 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:08.645 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:08.646  WARN 54336 --- [http-nio-8081-exec-2] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:08.646 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:08.648 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.649 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.PhotoMapper.insert            : ==>  Preparing: INSERT INTO ptm_photo ( user_id, group_id, title, description, url, thumbnail_url, storage_path, original_filename, file_type, width, height, location, visibility, allow_comment, allow_download, like_count, comment_count, collect_count, view_count, status, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:08.651 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.PhotoMapper.insert            : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e(String), b6e41e7c2d144ab32f285fc7b32bb9e.jpg(String), image/jpeg(String), 4608(Integer), 3072(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2025-05-27T15:22:08.649059800(LocalDateTime), 2025-05-27T15:22:08.649059800(LocalDateTime)
2025-05-27 15:22:08.652 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.PhotoMapper.insert            : <==    Updates: 1
2025-05-27 15:22:08.653 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.654 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.PhotoTagMapper.batchInsert    : ==>  Preparing: INSERT INTO ptm_photo_tag (photo_id, tag_name) VALUES (?, ?)
2025-05-27 15:22:08.654 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.PhotoTagMapper.batchInsert    : ==> Parameters: 85(Long), PhotoTag照片笔记(String)
2025-05-27 15:22:08.656 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.PhotoTagMapper.batchInsert    : <==    Updates: 1
2025-05-27 15:22:08.658  INFO 54336 --- [http-nio-8081-exec-2] c.p.service.impl.PhotoServiceImpl        : 照片已提交自动审核，照片ID: 85
2025-05-27 15:22:08.658  INFO 54336 --- [audit-task-3] com.phototagmoment.task.PhotoAuditTask   : 开始自动审核照片，照片ID: 85
2025-05-27 15:22:08.660 DEBUG 54336 --- [http-nio-8081-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:08.660 DEBUG 54336 --- [http-nio-8081-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=85, timestamp=1748330528660)]
2025-05-27 15:22:08.661 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,title,description,url,thumbnail_url,storage_path,file_type,width,height,location,visibility,allow_comment,allow_download,like_count,comment_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (id = ?)
2025-05-27 15:22:08.662 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:08.662 DEBUG 54336 --- [http-nio-8081-exec-2] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:08.662 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 85(Long)
2025-05-27 15:22:08.664 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectList        : <==      Total: 1
2025-05-27 15:22:08.665  INFO 54336 --- [audit-task-3] com.phototagmoment.task.PhotoAuditTask   : 文本内容审核不通过，照片ID: 85, 原因: 文本包含微信号
2025-05-27 15:22:08.667 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectById        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE id=? AND is_deleted=0
2025-05-27 15:22:08.668 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectById        : ==> Parameters: 85(Long)
2025-05-27 15:22:08.679 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.selectById        : <==      Total: 1
2025-05-27 15:22:08.681 DEBUG 54336 --- [audit-task-3] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.682 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoAuditMapper.insert       : ==>  Preparing: INSERT INTO ptm_photo_audit ( photo_id, audit_type, audit_result, reject_reason, audit_time, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:08.684 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoAuditMapper.insert       : ==> Parameters: 85(Long), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:08.680733200(LocalDateTime), 2025-05-27T15:22:08.682754300(LocalDateTime), 2025-05-27T15:22:08.682754300(LocalDateTime)
2025-05-27 15:22:08.686 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoAuditMapper.insert       : <==    Updates: 1
2025-05-27 15:22:08.687 DEBUG 54336 --- [audit-task-3] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充更新时间
2025-05-27 15:22:08.690 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.updateById        : ==>  Preparing: UPDATE ptm_photo SET user_id=?, group_id=?, title=?, description=?, url=?, thumbnail_url=?, storage_path=?, original_filename=?, file_type=?, width=?, height=?, location=?, visibility=?, allow_comment=?, allow_download=?, like_count=?, comment_count=?, collect_count=?, view_count=?, status=?, reject_reason=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-05-27 15:22:08.691 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.updateById        : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e(String), b6e41e7c2d144ab32f285fc7b32bb9e.jpg(String), image/jpeg(String), 4608(Integer), 3072(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:09(LocalDateTime), 2025-05-27T15:22:09(LocalDateTime), 85(Long)
2025-05-27 15:22:08.692 DEBUG 54336 --- [audit-task-3] c.p.mapper.PhotoMapper.updateById        : <==    Updates: 1
2025-05-27 15:22:08.698  INFO 54336 --- [audit-task-3] com.phototagmoment.task.PhotoAuditTask   : 自动审核不通过，照片ID: 85, 原因: 文本包含微信号
2025-05-27 15:22:08.701 DEBUG 54336 --- [audit-task-3] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.702 DEBUG 54336 --- [audit-task-3] c.p.mapper.NotificationMapper.insert     : ==>  Preparing: INSERT INTO ptm_notification ( user_id, type, content, is_read, created_at ) VALUES ( ?, ?, ?, ?, ? )
2025-05-27 15:22:08.703 DEBUG 54336 --- [audit-task-3] c.p.mapper.NotificationMapper.insert     : ==> Parameters: 1(Long), 5(Integer), 您的照片《PhotoTag照片笔记成都》未通过审核，原因：文本包含微信号(String), 0(Integer), 2025-05-27T15:22:08.702398900(LocalDateTime)
2025-05-27 15:22:08.704 DEBUG 54336 --- [audit-task-3] c.p.mapper.NotificationMapper.insert     : <==    Updates: 1
2025-05-27 15:22:08.705 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /photo/save-info
2025-05-27 15:22:08.705 DEBUG 54336 --- [http-nio-8081-exec-4] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:08.705  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:08.705 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/photo/save-info
2025-05-27 15:22:08.705  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:08.706  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:08.706  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.706  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:08.706  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:08.706  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/upload
2025-05-27 15:22:08.706  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:08.706  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:08.706  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:08.706  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:08.708  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.708  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:08.708  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:08.708  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:08.708  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:08.708  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:08.708  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:08.709  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.709  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:08.709  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   content-length = 739
2025-05-27 15:22:08.709  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:08.709  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:08.709  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.710  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:08.710  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:08.710  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/save-info
2025-05-27 15:22:08.713  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:08.716 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:08.718  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:08.718 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:08.718 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:08.720 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:08.721  WARN 54336 --- [http-nio-8081-exec-4] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:08.721  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:08.721  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:08.722  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:08.722 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /photo/save-info] with attributes [authenticated]
2025-05-27 15:22:08.722 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /photo/save-info
2025-05-27 15:22:08.723  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/photo/save-info
2025-05-27 15:22:08.723  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/photo/save-info
2025-05-27 15:22:08.723 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.web.servlet.DispatcherServlet        : POST "/api/photo/save-info", parameters={}
2025-05-27 15:22:08.724 DEBUG 54336 --- [http-nio-8081-exec-4] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#savePhotoInfo(PhotoUploadWithMentionsDTO)
2025-05-27 15:22:08.725 DEBUG 54336 --- [http-nio-8081-exec-4] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [PhotoUploadWithMentionsDTO(title=PhotoTag照片笔记成都, description=<span class="topic-tag" data-tag-name=" (truncated)...]
2025-05-27 15:22:08.725 DEBUG 54336 --- [http-nio-8081-exec-4] horizationManagerBeforeMethodInterceptor : Authorizing method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:08.726 DEBUG 54336 --- [http-nio-8081-exec-4] horizationManagerBeforeMethodInterceptor : Authorized method invocation ReflectiveMethodInvocation: public com.phototagmoment.common.ApiResponse com.phototagmoment.controller.PhotoController.savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO); target is of class [com.phototagmoment.controller.PhotoController]
2025-05-27 15:22:08.726 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:08.727 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:08.727 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:08.728 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:08.730  WARN 54336 --- [http-nio-8081-exec-4] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:08.730 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:08.731 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.732 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.PhotoMapper.insert            : ==>  Preparing: INSERT INTO ptm_photo ( user_id, group_id, title, description, url, thumbnail_url, storage_path, original_filename, file_type, width, height, location, visibility, allow_comment, allow_download, like_count, comment_count, collect_count, view_count, status, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:08.733 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.PhotoMapper.insert            : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375(String), c6ae50c58f1cb7b8c8de53462150e61.jpg(String), image/jpeg(String), 1080(Integer), 1620(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2025-05-27T15:22:08.732912(LocalDateTime), 2025-05-27T15:22:08.732912(LocalDateTime)
2025-05-27 15:22:08.735 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.PhotoMapper.insert            : <==    Updates: 1
2025-05-27 15:22:08.735 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.736 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.PhotoTagMapper.batchInsert    : ==>  Preparing: INSERT INTO ptm_photo_tag (photo_id, tag_name) VALUES (?, ?)
2025-05-27 15:22:08.736 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.PhotoTagMapper.batchInsert    : ==> Parameters: 86(Long), PhotoTag照片笔记(String)
2025-05-27 15:22:08.738 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.PhotoTagMapper.batchInsert    : <==    Updates: 1
2025-05-27 15:22:08.743  INFO 54336 --- [audit-task-4] com.phototagmoment.task.PhotoAuditTask   : 开始自动审核照片，照片ID: 86
2025-05-27 15:22:08.743  INFO 54336 --- [http-nio-8081-exec-4] c.p.service.impl.PhotoServiceImpl        : 照片已提交自动审核，照片ID: 86
2025-05-27 15:22:08.744 DEBUG 54336 --- [http-nio-8081-exec-4] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:08.745 DEBUG 54336 --- [http-nio-8081-exec-4] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=86, timestamp=1748330528744)]
2025-05-27 15:22:08.745 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,title,description,url,thumbnail_url,storage_path,file_type,width,height,location,visibility,allow_comment,allow_download,like_count,comment_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (id = ?)
2025-05-27 15:22:08.745 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 86(Long)
2025-05-27 15:22:08.746 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:08.747 DEBUG 54336 --- [http-nio-8081-exec-4] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:08.748 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectList        : <==      Total: 1
2025-05-27 15:22:08.749  INFO 54336 --- [audit-task-4] com.phototagmoment.task.PhotoAuditTask   : 文本内容审核不通过，照片ID: 86, 原因: 文本包含微信号
2025-05-27 15:22:08.752 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectById        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE id=? AND is_deleted=0
2025-05-27 15:22:08.752 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectById        : ==> Parameters: 86(Long)
2025-05-27 15:22:08.754 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.selectById        : <==      Total: 1
2025-05-27 15:22:08.755 DEBUG 54336 --- [audit-task-4] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.755 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoAuditMapper.insert       : ==>  Preparing: INSERT INTO ptm_photo_audit ( photo_id, audit_type, audit_result, reject_reason, audit_time, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-05-27 15:22:08.756 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoAuditMapper.insert       : ==> Parameters: 86(Long), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:08.754992600(LocalDateTime), 2025-05-27T15:22:08.755994600(LocalDateTime), 2025-05-27T15:22:08.755994600(LocalDateTime)
2025-05-27 15:22:08.758 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoAuditMapper.insert       : <==    Updates: 1
2025-05-27 15:22:08.759 DEBUG 54336 --- [audit-task-4] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充更新时间
2025-05-27 15:22:08.761 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.updateById        : ==>  Preparing: UPDATE ptm_photo SET user_id=?, group_id=?, title=?, description=?, url=?, thumbnail_url=?, storage_path=?, original_filename=?, file_type=?, width=?, height=?, location=?, visibility=?, allow_comment=?, allow_download=?, like_count=?, comment_count=?, collect_count=?, view_count=?, status=?, reject_reason=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-05-27 15:22:08.762 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.updateById        : ==> Parameters: 1(Long), 1748330525487-y0x4pxey5(String), PhotoTag照片笔记成都(String), <span class="topic-tag" data-tag-name="PhotoTag照片笔记">#PhotoTag照片笔记&nbsp;照片笔记PhotoTag 成都</span>(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375(String), http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375?imageView2/2/w/400(String), phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375(String), c6ae50c58f1cb7b8c8de53462150e61.jpg(String), image/jpeg(String), 1080(Integer), 1620(Integer), (String), 1(Integer), 1(Integer), 1(Integer), 0(Integer), 0(Integer), 0(Integer), 0(Integer), 2(Integer), 文本包含微信号(String), 2025-05-27T15:22:09(LocalDateTime), 2025-05-27T15:22:09(LocalDateTime), 86(Long)
2025-05-27 15:22:08.765 DEBUG 54336 --- [audit-task-4] c.p.mapper.PhotoMapper.updateById        : <==    Updates: 1
2025-05-27 15:22:08.770  INFO 54336 --- [audit-task-4] com.phototagmoment.task.PhotoAuditTask   : 自动审核不通过，照片ID: 86, 原因: 文本包含微信号
2025-05-27 15:22:08.773 DEBUG 54336 --- [audit-task-4] c.p.config.MyBatisPlusMetaObjectHandler  : 自动填充创建时间和更新时间
2025-05-27 15:22:08.773 DEBUG 54336 --- [audit-task-4] c.p.mapper.NotificationMapper.insert     : ==>  Preparing: INSERT INTO ptm_notification ( user_id, type, content, is_read, created_at ) VALUES ( ?, ?, ?, ?, ? )
2025-05-27 15:22:08.774 DEBUG 54336 --- [audit-task-4] c.p.mapper.NotificationMapper.insert     : ==> Parameters: 1(Long), 5(Integer), 您的照片《PhotoTag照片笔记成都》未通过审核，原因：文本包含微信号(String), 0(Integer), 2025-05-27T15:22:08.773124200(LocalDateTime)
2025-05-27 15:22:08.775 DEBUG 54336 --- [audit-task-4] c.p.mapper.NotificationMapper.insert     : <==    Updates: 1
2025-05-27 15:22:10.647 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /photo-notes/78?_t=1748330530590
2025-05-27 15:22:10.647 DEBUG 54336 --- [http-nio-8081-exec-6] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:10.647  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo-notes/78
2025-05-27 15:22:10.648 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/photo-notes/78
2025-05-27 15:22:10.648  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /photo-notes
2025-05-27 15:22:10.648  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:10.648  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:10.648  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:10.648  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:10.648  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:10.649  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/photo/detail/78
2025-05-27 15:22:10.649  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:10.650  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:10.650  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:10.650  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:10.650  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:10.650  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:10.651  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:10.651  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:10.651  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:10.651  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:10.651  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:10.651  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:10.652  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:10.652  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:10.652  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:10.652  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:10.652  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo-notes/78
2025-05-27 15:22:10.654  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:10.655 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:10.656  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:10.656 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:10.657 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:10.658 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:10.658  WARN 54336 --- [http-nio-8081-exec-6] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:10.659  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:10.659  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:10.659  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:10.660  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/photo-notes/78
2025-05-27 15:22:10.660 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /photo-notes/78?_t=1748330530590] with attributes [permitAll]
2025-05-27 15:22:10.660 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /photo-notes/78?_t=1748330530590
2025-05-27 15:22:10.661  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo-notes/78
2025-05-27 15:22:10.661  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/photo-notes/78
2025-05-27 15:22:10.661 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.web.servlet.DispatcherServlet        : GET "/api/photo-notes/78?_t=1748330530590", parameters={masked}
2025-05-27 15:22:10.664 DEBUG 54336 --- [http-nio-8081-exec-6] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoNoteController#getPhotoNoteDetail(Long, HttpServletRequest)
2025-05-27 15:22:11.473  WARN 54336 --- [http-nio-8081-exec-6] c.p.service.impl.PhotoNoteServiceImpl    : Redis操作失败，无法检查浏览记录缓存: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
2025-05-27 15:22:11.475 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.incrementViewCount               : ==>  Preparing: UPDATE ptm_photo_note SET view_count = view_count + 1 WHERE id = ?
2025-05-27 15:22:11.476 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.incrementViewCount               : ==> Parameters: 78(Long)
2025-05-27 15:22:11.477 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.incrementViewCount               : <==    Updates: 0
2025-05-27 15:22:11.484  WARN 54336 --- [http-nio-8081-exec-6] c.p.service.impl.PhotoNoteServiceImpl    : Redis操作失败，无法设置浏览记录缓存: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
2025-05-27 15:22:11.485 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectTagsByNoteId               : ==>  Preparing: SELECT tag_name FROM ptm_photo_note_tag WHERE note_id = ?
2025-05-27 15:22:11.486 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectTagsByNoteId               : ==> Parameters: 78(Long)
2025-05-27 15:22:11.486 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectTagsByNoteId               : <==      Total: 0
2025-05-27 15:22:11.488 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectPhotoNoteDetail            : ==>  Preparing: SELECT pn.id, pn.user_id, u.nickname, u.avatar, pn.title, pn.content, pn.photo_count, pn.view_count, pn.like_count, pn.comment_count, pn.share_count, pn.visibility, pn.allow_comment, pn.status, pn.reject_reason, pn.location, pn.longitude, pn.latitude, pn.created_at, pn.updated_at ,false as is_liked, false as is_collected FROM ptm_photo_note pn LEFT JOIN ptm_user u ON pn.user_id = u.id WHERE pn.id = ? AND pn.is_deleted = 0
2025-05-27 15:22:11.488 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectPhotoNoteDetail            : ==> Parameters: 78(Long)
2025-05-27 15:22:11.490 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectPhotoNoteDetail            : <==      Total: 0
2025-05-27 15:22:11.492 DEBUG 54336 --- [http-nio-8081-exec-6] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:11.492 DEBUG 54336 --- [http-nio-8081-exec-6] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=500, message=照片笔记不存在, data=null, timestamp=1748330531490)]
2025-05-27 15:22:11.493 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:11.493 DEBUG 54336 --- [http-nio-8081-exec-6] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:11.505 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /photo/detail/78?includeGroupPhotos=true&_t=1748330531500
2025-05-27 15:22:11.506 DEBUG 54336 --- [http-nio-8081-exec-7] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:11.506  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo/detail/78
2025-05-27 15:22:11.506 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/photo/detail/78
2025-05-27 15:22:11.506  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /photo/detail
2025-05-27 15:22:11.506  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:11.506  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:11.507  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:11.507  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:11.507  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:11.508  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/photo/detail/78
2025-05-27 15:22:11.508  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:11.508  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:11.508  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:11.508  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:11.508  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:11.508  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:11.509  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:11.509  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:11.509  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:11.509  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:11.509  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:11.509  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:11.510  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:11.510  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:11.510  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:11.510  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:11.510  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/detail/78
2025-05-27 15:22:11.514  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:11.516 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:11.516  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:11.516 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:11.516 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:11.518 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:11.519  WARN 54336 --- [http-nio-8081-exec-7] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:11.519  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:11.519  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:11.519  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:11.520  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/photo/detail/78
2025-05-27 15:22:11.520 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /photo/detail/78?includeGroupPhotos=true&_t=1748330531500] with attributes [permitAll]
2025-05-27 15:22:11.520 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /photo/detail/78?includeGroupPhotos=true&_t=1748330531500
2025-05-27 15:22:11.521  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo/detail/78
2025-05-27 15:22:11.521  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/photo/detail/78
2025-05-27 15:22:11.521 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.web.servlet.DispatcherServlet        : GET "/api/photo/detail/78?includeGroupPhotos=true&_t=1748330531500", parameters={masked}
2025-05-27 15:22:11.523 DEBUG 54336 --- [http-nio-8081-exec-7] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#getPhotoDetail(Long, Boolean)
2025-05-27 15:22:11.525 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:11.525 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:11.525 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:11.527 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:11.528  WARN 54336 --- [http-nio-8081-exec-7] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:11.529 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:11.531 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.m.PhotoMapper.selectPhotoDetail      : ==>  Preparing: SELECT p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path, p.original_filename, p.file_type, p.width, p.height, p.location, p.visibility, p.allow_comment, p.allow_download, p.like_count, p.comment_count, p.collect_count, p.view_count, p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at, u.username AS userName, u.nickname AS userNickname, u.avatar AS userAvatar, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked, CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected FROM ptm_photo p LEFT JOIN ptm_user u ON p.user_id = u.id LEFT JOIN ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = ? LEFT JOIN ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = ? WHERE p.id = ?
2025-05-27 15:22:11.531 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.m.PhotoMapper.selectPhotoDetail      : ==> Parameters: 1(Long), 1(Long), 78(Long)
2025-05-27 15:22:11.535 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.m.PhotoMapper.selectPhotoDetail      : <==      Total: 1
2025-05-27 15:22:11.537 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.m.PhotoMapper.incrementViewCount     : ==>  Preparing: UPDATE ptm_photo SET view_count = view_count + 1 WHERE id = ?
2025-05-27 15:22:11.537 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.m.PhotoMapper.incrementViewCount     : ==> Parameters: 78(Long)
2025-05-27 15:22:11.545 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.m.PhotoMapper.incrementViewCount     : <==    Updates: 1
2025-05-27 15:22:11.546  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910
2025-05-27 15:22:11.547  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910?imageView2/2/w/400
2025-05-27 15:22:11.550 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (group_id = ? AND is_deleted = ?) ORDER BY created_at ASC
2025-05-27 15:22:11.551 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 1748330525487-y0x4pxey5(String), 0(Integer)
2025-05-27 15:22:11.555 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.PhotoMapper.selectList        : <==      Total: 9
2025-05-27 15:22:11.557 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.m.P.selectPhotoDetailsByIds          : ==>  Preparing: SELECT p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path, p.original_filename, p.file_type, p.width, p.height, p.location, p.visibility, p.allow_comment, p.allow_download, p.like_count, p.comment_count, p.collect_count, p.view_count, p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at, u.username AS userName, u.nickname AS userNickname, u.avatar AS userAvatar, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked, CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected FROM ptm_photo p LEFT JOIN ptm_user u ON p.user_id = u.id LEFT JOIN ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = ? LEFT JOIN ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = ? WHERE p.id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? ) ORDER BY FIELD(p.id, ? , ? , ? , ? , ? , ? , ? , ? , ? )
2025-05-27 15:22:11.558 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.m.P.selectPhotoDetailsByIds          : ==> Parameters: 1(Long), 1(Long), 78(Long), 79(Long), 80(Long), 81(Long), 82(Long), 83(Long), 84(Long), 85(Long), 86(Long), 78(Long), 79(Long), 80(Long), 81(Long), 82(Long), 83(Long), 84(Long), 85(Long), 86(Long)
2025-05-27 15:22:11.561 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.m.P.selectPhotoDetailsByIds          : <==      Total: 9
2025-05-27 15:22:11.562  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910
2025-05-27 15:22:11.563  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910?imageView2/2/w/400
2025-05-27 15:22:11.563  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63
2025-05-27 15:22:11.563  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63?imageView2/2/w/400
2025-05-27 15:22:11.564  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6
2025-05-27 15:22:11.564  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6?imageView2/2/w/400
2025-05-27 15:22:11.565  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7
2025-05-27 15:22:11.565  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7?imageView2/2/w/400
2025-05-27 15:22:11.565  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645
2025-05-27 15:22:11.565  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645?imageView2/2/w/400
2025-05-27 15:22:11.566  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38
2025-05-27 15:22:11.566  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38?imageView2/2/w/400
2025-05-27 15:22:11.566  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca
2025-05-27 15:22:11.566  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca?imageView2/2/w/400
2025-05-27 15:22:11.566  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e
2025-05-27 15:22:11.566  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e?imageView2/2/w/400
2025-05-27 15:22:11.567  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375
2025-05-27 15:22:11.567  INFO 54336 --- [http-nio-8081-exec-7] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375?imageView2/2/w/400
2025-05-27 15:22:11.568 DEBUG 54336 --- [http-nio-8081-exec-7] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:11.597 DEBUG 54336 --- [http-nio-8081-exec-7] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=PhotoDTO(id=78, userId=1, groupId=1748330525487-y0x4pxey5,  (truncated)...]
2025-05-27 15:22:11.600 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:11.601 DEBUG 54336 --- [http-nio-8081-exec-7] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:11.610 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /photo/group/1748330525487-y0x4pxey5?_t=1748330531604
2025-05-27 15:22:11.610 DEBUG 54336 --- [http-nio-8081-exec-8] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:11.610  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo/group/1748330525487-y0x4pxey5
2025-05-27 15:22:11.610 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/photo/group/1748330525487-y0x4pxey5
2025-05-27 15:22:11.610  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:11.610  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:11.611  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:11.611  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:11.611  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:11.611  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/photo/detail/78
2025-05-27 15:22:11.611  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:11.611  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:11.611  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:11.612  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:11.612  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:11.612  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:11.612  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:11.612  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:11.612  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:11.612  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:11.612  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:11.613  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:11.613  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:11.613  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:11.613  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:11.613  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:11.614  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/group/1748330525487-y0x4pxey5
2025-05-27 15:22:11.616  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:11.617 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:11.617  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:11.618 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:11.619 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:11.620 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:11.621  WARN 54336 --- [http-nio-8081-exec-8] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:11.621  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:11.622  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:11.622  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:11.622 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /photo/group/1748330525487-y0x4pxey5?_t=1748330531604] with attributes [permitAll]
2025-05-27 15:22:11.623 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /photo/group/1748330525487-y0x4pxey5?_t=1748330531604
2025-05-27 15:22:11.623  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo/group/1748330525487-y0x4pxey5
2025-05-27 15:22:11.623  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/photo/group/1748330525487-y0x4pxey5
2025-05-27 15:22:11.623 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.web.servlet.DispatcherServlet        : GET "/api/photo/group/1748330525487-y0x4pxey5?_t=1748330531604", parameters={masked}
2025-05-27 15:22:11.625 DEBUG 54336 --- [http-nio-8081-exec-8] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#getPhotosByGroupId(String)
2025-05-27 15:22:11.626 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:11.627 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:11.628 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:11.631 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:11.632  WARN 54336 --- [http-nio-8081-exec-8] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:11.632 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:11.634 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.PhotoMapper.selectList        : ==>  Preparing: SELECT id,user_id,group_id,title,description,url,thumbnail_url,storage_path,original_filename,file_size,file_type,width,height,location,taken_time,visibility,allow_comment,allow_download,like_count,comment_count,collect_count,view_count,status,reject_reason,is_deleted,created_at,updated_at FROM ptm_photo WHERE is_deleted=0 AND (group_id = ? AND is_deleted = ?) ORDER BY created_at ASC
2025-05-27 15:22:11.635 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.PhotoMapper.selectList        : ==> Parameters: 1748330525487-y0x4pxey5(String), 0(Integer)
2025-05-27 15:22:11.637 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.PhotoMapper.selectList        : <==      Total: 9
2025-05-27 15:22:11.639 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.m.P.selectPhotoDetailsByIds          : ==>  Preparing: SELECT p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path, p.original_filename, p.file_type, p.width, p.height, p.location, p.visibility, p.allow_comment, p.allow_download, p.like_count, p.comment_count, p.collect_count, p.view_count, p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at, u.username AS userName, u.nickname AS userNickname, u.avatar AS userAvatar, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked, CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected FROM ptm_photo p LEFT JOIN ptm_user u ON p.user_id = u.id LEFT JOIN ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = ? LEFT JOIN ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = ? WHERE p.id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? ) ORDER BY FIELD(p.id, ? , ? , ? , ? , ? , ? , ? , ? , ? )
2025-05-27 15:22:11.640 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.m.P.selectPhotoDetailsByIds          : ==> Parameters: 1(Long), 1(Long), 78(Long), 79(Long), 80(Long), 81(Long), 82(Long), 83(Long), 84(Long), 85(Long), 86(Long), 78(Long), 79(Long), 80(Long), 81(Long), 82(Long), 83(Long), 84(Long), 85(Long), 86(Long)
2025-05-27 15:22:11.647 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.m.P.selectPhotoDetailsByIds          : <==      Total: 9
2025-05-27 15:22:11.648  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910
2025-05-27 15:22:11.649  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/a63c9ba9ab214cfaba3cbcb354436910?imageView2/2/w/400
2025-05-27 15:22:11.649  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63
2025-05-27 15:22:11.649  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/1dc77d2b50ad403c8e6f83bb8a085c63?imageView2/2/w/400
2025-05-27 15:22:11.650  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6
2025-05-27 15:22:11.650  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/58d68ffea00242f1a0713cfdfb119ee6?imageView2/2/w/400
2025-05-27 15:22:11.650  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7
2025-05-27 15:22:11.650  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/ea5ab01497af42328beb5017190129a7?imageView2/2/w/400
2025-05-27 15:22:11.650  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645
2025-05-27 15:22:11.651  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/e4ce41f4f60e48cf856e03db563af645?imageView2/2/w/400
2025-05-27 15:22:11.651  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38
2025-05-27 15:22:11.651  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/d1f3b1882b8b4eb4ba49b3ebbdca5d38?imageView2/2/w/400
2025-05-27 15:22:11.651  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca
2025-05-27 15:22:11.652  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/73bd1aed3cac4e638fe7b292c9f009ca?imageView2/2/w/400
2025-05-27 15:22:11.652  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e
2025-05-27 15:22:11.652  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/5b61bb8599d6446eaaae629d80b6f07e?imageView2/2/w/400
2025-05-27 15:22:11.652  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375
2025-05-27 15:22:11.652  INFO 54336 --- [http-nio-8081-exec-8] c.p.s.impl.QiniuStorageServiceImpl       : 生成私有空间文件访问URL成功，文件名: phototagmoment/photos/1/2025/05/27/d94ccaf09f9e43929a416896acc1c375?imageView2/2/w/400
2025-05-27 15:22:11.653 DEBUG 54336 --- [http-nio-8081-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:11.653 DEBUG 54336 --- [http-nio-8081-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=[PhotoDTO(id=78, userId=1, groupId=1748330525487-y0x4pxey5, (truncated)...]
2025-05-27 15:22:11.656 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:11.656 DEBUG 54336 --- [http-nio-8081-exec-8] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:12.013 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /recommendation/record-behavior
2025-05-27 15:22:12.013 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/recommended?page=1&size=6&_t=1748330531673
2025-05-27 15:22:12.014 DEBUG 54336 --- [http-nio-8081-exec-10] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:12.013 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /photo/comments?photoId=78&page=1&size=10&_t=1748330531673
2025-05-27 15:22:12.014 DEBUG 54336 --- [http-nio-8081-exec-3] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:12.014 DEBUG 54336 --- [http-nio-8081-exec-9] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:12.014  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/recommendation/record-behavior
2025-05-27 15:22:12.014  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/recommended
2025-05-27 15:22:12.014  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo/comments
2025-05-27 15:22:12.014 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/recommendation/record-behavior
2025-05-27 15:22:12.014 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/recommended
2025-05-27 15:22:12.014 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/photo/comments
2025-05-27 15:22:12.015  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:22:12.015  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:22:12.015  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /photo/comments
2025-05-27 15:22:12.015  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:12.015  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:12.015  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:12.015  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:12.015  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:12.016  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.016  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:12.016  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.016  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:12.016  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.017  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:12.017  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:12.017  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:12.017  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:12.017  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/photo/detail/78
2025-05-27 15:22:12.017  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:12.017  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/photo/detail/78
2025-05-27 15:22:12.017  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:12.017  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/photo/detail/78
2025-05-27 15:22:12.017  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:12.018  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:12.018  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:12.018  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:12.018  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:12.018  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:12.018  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:12.018  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.018  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:12.019  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.019  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:12.019  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:12.019  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:12.019  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:12.019  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.019  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:12.019  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:12.020  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:12.020  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:12.020  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:12.021  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:12.021  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:12.021  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:12.021  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:12.021  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:12.021  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.022  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:12.022  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.022  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:12.022  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:12.022  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:12.022  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:12.022  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:12.022  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:12.022  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:12.022  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:12.023  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.023  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.023  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.023  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:12.023  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.023  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.023  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   content-length = 32
2025-05-27 15:22:12.023  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:12.024  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:12.024  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:12.024  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/comments
2025-05-27 15:22:12.024  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/recommended
2025-05-27 15:22:12.024 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /photo/comments?photoId=78&page=1&size=10&_t=1748330531709
2025-05-27 15:22:12.024  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:12.025  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.025  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.025  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:12.025 DEBUG 54336 --- [http-nio-8081-exec-1] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:12.025  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/record-behavior
2025-05-27 15:22:12.025  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo/comments
2025-05-27 15:22:12.026 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/photo/comments
2025-05-27 15:22:12.026  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /photo/comments
2025-05-27 15:22:12.026  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:12.026  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:12.026  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.026  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:12.026  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:12.029  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:12.031 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:12.032  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:12.027  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:12.031 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:12.033  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:12.033  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:12.033 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:12.033  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/photo/detail/78
2025-05-27 15:22:12.033 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:12.033 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:12.034 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:12.033  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:12.034  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:12.034  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:12.034 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:12.034  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.035  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:12.035  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:12.035 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:12.035 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:12.035  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:12.035  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:12.036 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:12.036  WARN 54336 --- [http-nio-8081-exec-9] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:12.036  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:12.036  WARN 54336 --- [http-nio-8081-exec-3] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:12.036 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:12.036  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:12.036  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:12.037  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:12.037  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:12.037  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.037  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:12.037  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:12.037  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:12.037  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:12.037  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/photo/comments
2025-05-27 15:22:12.037  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/recommended
2025-05-27 15:22:12.037  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:12.038  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:12.038 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:12.038  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.038 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /photo/comments?photoId=78&page=1&size=10&_t=1748330531673] with attributes [permitAll]
2025-05-27 15:22:12.038 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/recommended?page=1&size=6&_t=1748330531673] with attributes [permitAll]
2025-05-27 15:22:12.038  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.038 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/recommended?page=1&size=6&_t=1748330531673
2025-05-27 15:22:12.038 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /photo/comments?photoId=78&page=1&size=10&_t=1748330531673
2025-05-27 15:22:12.038  WARN 54336 --- [http-nio-8081-exec-10] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:12.038  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:12.038  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/recommended
2025-05-27 15:22:12.038  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo/comments
2025-05-27 15:22:12.039  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/photo/comments
2025-05-27 15:22:12.039  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:12.039  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/recommended
2025-05-27 15:22:12.039  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/photo/comments
2025-05-27 15:22:12.039  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:12.039 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/recommended?page=1&size=6&_t=1748330531673", parameters={masked}
2025-05-27 15:22:12.040 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.web.servlet.DispatcherServlet        : GET "/api/photo/comments?photoId=78&page=1&size=10&_t=1748330531673", parameters={masked}
2025-05-27 15:22:12.040  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:12.040 DEBUG 54336 --- [http-nio-8081-exec-9] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#getPhotoComments(Long, Integer, Integer)
2025-05-27 15:22:12.040 DEBUG 54336 --- [http-nio-8081-exec-3] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getRecommendedPhotos(int, int)
2025-05-27 15:22:12.040  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/record-behavior
2025-05-27 15:22:12.040 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /recommendation/record-behavior] with attributes [permitAll]
2025-05-27 15:22:12.041 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /recommendation/record-behavior
2025-05-27 15:22:12.041  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/recommendation/record-behavior
2025-05-27 15:22:12.041  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/recommendation/record-behavior
2025-05-27 15:22:12.041 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.web.servlet.DispatcherServlet        : POST "/api/recommendation/record-behavior", parameters={}
2025-05-27 15:22:12.041  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:12.041  INFO 54336 --- [http-nio-8081-exec-3] c.p.s.impl.RecommendationServiceImpl     : 获取热门照片笔记，页码：1，每页大小：6
2025-05-27 15:22:12.042 DEBUG 54336 --- [http-nio-8081-exec-10] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#recordUserBehavior(Long, String, BehaviorRequest)
2025-05-27 15:22:12.042 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:12.042 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:12.043 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:12.044 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:12.044 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:12.045  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:12.045  WARN 54336 --- [http-nio-8081-exec-9] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:12.045 DEBUG 54336 --- [http-nio-8081-exec-10] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [RecommendationController.BehaviorRequest(noteId=null, behavior=view)]
2025-05-27 15:22:12.045 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:12.046  WARN 54336 --- [http-nio-8081-exec-10] c.p.controller.RecommendationController  : 记录用户行为失败：照片笔记ID为空
2025-05-27 15:22:12.046 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:12.046 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:12.047 DEBUG 54336 --- [http-nio-8081-exec-10] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:12.047 DEBUG 54336 --- [http-nio-8081-exec-10] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=500, message=照片笔记ID不能为空, data=null)]
2025-05-27 15:22:12.049 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:12.049 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:12.050  WARN 54336 --- [http-nio-8081-exec-1] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:12.050 DEBUG 54336 --- [http-nio-8081-exec-10] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:12.050  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:12.050  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:12.050  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:12.050  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/photo/comments
2025-05-27 15:22:12.051 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /photo/comments?photoId=78&page=1&size=10&_t=1748330531709] with attributes [permitAll]
2025-05-27 15:22:12.051 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /photo/comments?photoId=78&page=1&size=10&_t=1748330531709
2025-05-27 15:22:12.051  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/photo/comments
2025-05-27 15:22:12.052  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/photo/comments
2025-05-27 15:22:12.052 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.m.P.selectHotPhotoNotes_mpCount      : ==>  Preparing: SELECT COUNT(*) AS total FROM ptm_photo_note pn WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
2025-05-27 15:22:12.052 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : GET "/api/photo/comments?photoId=78&page=1&size=10&_t=1748330531709", parameters={masked}
2025-05-27 15:22:12.053 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.m.P.selectHotPhotoNotes_mpCount      : ==> Parameters: 7(Integer)
2025-05-27 15:22:12.053 DEBUG 54336 --- [http-nio-8081-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.PhotoController#getPhotoComments(Long, Integer, Integer)
2025-05-27 15:22:12.054 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.s.impl.AuthenticationServiceImpl     : 从UserDetails获取用户名: test
2025-05-27 15:22:12.054 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:12.054 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.m.P.selectHotPhotoNotes_mpCount      : <==      Total: 1
2025-05-27 15:22:12.055 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:12.055 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.m.P.selectHotPhotoNotes              : ==>  Preparing: SELECT pn.id, pn.user_id, u.nickname, u.avatar, pn.title, pn.content, pn.photo_count, pn.view_count, pn.like_count, pn.comment_count, pn.share_count, pn.visibility, pn.allow_comment, pn.status, pn.reject_reason, pn.location, pn.longitude, pn.latitude, pn.created_at, pn.updated_at ,false as is_liked, false as is_collected FROM ptm_photo_note pn LEFT JOIN ptm_user u ON pn.user_id = u.id WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC LIMIT ?
2025-05-27 15:22:12.055 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.m.P.selectHotPhotoNotes              : ==> Parameters: 7(Integer), 6(Long)
2025-05-27 15:22:12.056 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:12.057  WARN 54336 --- [http-nio-8081-exec-1] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:12.057 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.s.impl.AuthenticationServiceImpl     : 成功获取当前用户: test
2025-05-27 15:22:12.057 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.m.P.selectHotPhotoNotes              : <==      Total: 3
2025-05-27 15:22:12.058 DEBUG 54336 --- [http-nio-8081-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:12.058 DEBUG 54336 --- [http-nio-8081-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@48b12 (truncated)...]
2025-05-27 15:22:12.060 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:12.060 DEBUG 54336 --- [http-nio-8081-exec-3] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:12.073 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.m.C.selectPhotoComments_mpCount      : ==>  Preparing: SELECT COUNT(*) AS total FROM ptm_comment c LEFT JOIN ptm_user u ON c.user_id = u.id LEFT JOIN ptm_comment_like cl ON c.id = cl.comment_id AND cl.user_id = ? AND ? IS NOT NULL WHERE c.photo_id = ? AND c.parent_id IS NULL AND c.is_deleted = 0
2025-05-27 15:22:12.073 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.m.C.selectPhotoComments_mpCount      : ==>  Preparing: SELECT COUNT(*) AS total FROM ptm_comment c LEFT JOIN ptm_user u ON c.user_id = u.id LEFT JOIN ptm_comment_like cl ON c.id = cl.comment_id AND cl.user_id = ? AND ? IS NOT NULL WHERE c.photo_id = ? AND c.parent_id IS NULL AND c.is_deleted = 0
2025-05-27 15:22:12.073 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.m.C.selectPhotoComments_mpCount      : ==> Parameters: 1(Long), 1(Long), 78(Long)
2025-05-27 15:22:12.073 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.m.C.selectPhotoComments_mpCount      : ==> Parameters: 1(Long), 1(Long), 78(Long)
2025-05-27 15:22:12.075 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.m.C.selectPhotoComments_mpCount      : <==      Total: 1
2025-05-27 15:22:12.075 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.m.C.selectPhotoComments_mpCount      : <==      Total: 1
2025-05-27 15:22:12.076 DEBUG 54336 --- [http-nio-8081-exec-9] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:12.076 DEBUG 54336 --- [http-nio-8081-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:12.076 DEBUG 54336 --- [http-nio-8081-exec-9] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@ (truncated)...]
2025-05-27 15:22:12.076 DEBUG 54336 --- [http-nio-8081-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@ (truncated)...]
2025-05-27 15:22:12.077 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:12.077 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:12.078 DEBUG 54336 --- [http-nio-8081-exec-9] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:12.078 DEBUG 54336 --- [http-nio-8081-exec-1] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:12.272 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/recommended?page=1&size=6&_t=1748330532086
2025-05-27 15:22:12.272 DEBUG 54336 --- [http-nio-8081-exec-5] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:12.273  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/recommended
2025-05-27 15:22:12.273 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/recommended
2025-05-27 15:22:12.273  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:22:12.273  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:12.273  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:12.273  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.273  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:12.274  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:12.274  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/photo/detail/78
2025-05-27 15:22:12.274  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:12.274  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:12.274  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:12.274  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.274  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:12.274  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:12.275  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:12.275  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:12.275  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:12.275  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.275  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:12.275  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:12.275  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:12.275  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.275  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.276  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:12.276  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/recommended
2025-05-27 15:22:12.277  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:12.279 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:12.280  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:12.280 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:12.281 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:12.283 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:12.283  WARN 54336 --- [http-nio-8081-exec-5] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:12.283  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:12.283  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:12.283  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:12.283  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/recommended
2025-05-27 15:22:12.284 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/recommended?page=1&size=6&_t=1748330532086] with attributes [permitAll]
2025-05-27 15:22:12.284 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/recommended?page=1&size=6&_t=1748330532086
2025-05-27 15:22:12.284  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/recommended
2025-05-27 15:22:12.285  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/recommended
2025-05-27 15:22:12.285 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/recommended?page=1&size=6&_t=1748330532086", parameters={masked}
2025-05-27 15:22:12.286 DEBUG 54336 --- [http-nio-8081-exec-5] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getRecommendedPhotos(int, int)
2025-05-27 15:22:12.286  INFO 54336 --- [http-nio-8081-exec-5] c.p.s.impl.RecommendationServiceImpl     : 获取热门照片笔记，页码：1，每页大小：6
2025-05-27 15:22:12.291 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.m.P.selectHotPhotoNotes_mpCount      : ==>  Preparing: SELECT COUNT(*) AS total FROM ptm_photo_note pn WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
2025-05-27 15:22:12.291 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.m.P.selectHotPhotoNotes_mpCount      : ==> Parameters: 7(Integer)
2025-05-27 15:22:12.292 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.m.P.selectHotPhotoNotes_mpCount      : <==      Total: 1
2025-05-27 15:22:12.293 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.m.P.selectHotPhotoNotes              : ==>  Preparing: SELECT pn.id, pn.user_id, u.nickname, u.avatar, pn.title, pn.content, pn.photo_count, pn.view_count, pn.like_count, pn.comment_count, pn.share_count, pn.visibility, pn.allow_comment, pn.status, pn.reject_reason, pn.location, pn.longitude, pn.latitude, pn.created_at, pn.updated_at ,false as is_liked, false as is_collected FROM ptm_photo_note pn LEFT JOIN ptm_user u ON pn.user_id = u.id WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC LIMIT ?
2025-05-27 15:22:12.293 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.m.P.selectHotPhotoNotes              : ==> Parameters: 7(Integer), 6(Long)
2025-05-27 15:22:12.295 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.m.P.selectHotPhotoNotes              : <==      Total: 3
2025-05-27 15:22:12.297 DEBUG 54336 --- [http-nio-8081-exec-5] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:12.297 DEBUG 54336 --- [http-nio-8081-exec-5] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@76111 (truncated)...]
2025-05-27 15:22:12.298 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:12.298 DEBUG 54336 --- [http-nio-8081-exec-5] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:12.392 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /recommendation/record-behavior
2025-05-27 15:22:12.392 DEBUG 54336 --- [http-nio-8081-exec-2] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:12.393  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/recommendation/record-behavior
2025-05-27 15:22:12.393 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 处理请求详情: POST /api/recommendation/record-behavior
2025-05-27 15:22:12.393  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:22:12.393  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:12.393  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:12.393  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.394  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:12.394  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:12.394  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/photo/detail/78
2025-05-27 15:22:12.394  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:12.395  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:12.395  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:12.395  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   origin = http://localhost:3000
2025-05-27 15:22:12.395  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.395  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   content-type = application/json;charset=UTF-8
2025-05-27 15:22:12.395  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:12.396  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:12.396  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:12.396  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:12.396  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:12.396  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.396  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:12.397  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   content-length = 32
2025-05-27 15:22:12.397  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:12.397  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:12.397  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.397  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:12.398  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:12.398  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/record-behavior
2025-05-27 15:22:12.400  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:12.402 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:12.402  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:12.403 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:12.403 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:12.405 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:12.405  WARN 54336 --- [http-nio-8081-exec-2] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:12.405  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:12.406  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:12.406  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:12.406  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/record-behavior
2025-05-27 15:22:12.407 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [POST /recommendation/record-behavior] with attributes [permitAll]
2025-05-27 15:22:12.407 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /recommendation/record-behavior
2025-05-27 15:22:12.407  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: POST /api/recommendation/record-behavior
2025-05-27 15:22:12.407  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: POST /api/recommendation/record-behavior
2025-05-27 15:22:12.407 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.web.servlet.DispatcherServlet        : POST "/api/recommendation/record-behavior", parameters={}
2025-05-27 15:22:12.407 DEBUG 54336 --- [http-nio-8081-exec-2] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#recordUserBehavior(Long, String, BehaviorRequest)
2025-05-27 15:22:12.408 DEBUG 54336 --- [http-nio-8081-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [RecommendationController.BehaviorRequest(noteId=null, behavior=view)]
2025-05-27 15:22:12.408  WARN 54336 --- [http-nio-8081-exec-2] c.p.controller.RecommendationController  : 记录用户行为失败：照片笔记ID为空
2025-05-27 15:22:12.408 DEBUG 54336 --- [http-nio-8081-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:12.409 DEBUG 54336 --- [http-nio-8081-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=500, message=照片笔记ID不能为空, data=null)]
2025-05-27 15:22:12.409 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:12.410 DEBUG 54336 --- [http-nio-8081-exec-2] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:26.281 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /search/popular-tags?limit=10&_t=1748330546081
2025-05-27 15:22:26.281 DEBUG 54336 --- [http-nio-8081-exec-4] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:26.282  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/search/popular-tags
2025-05-27 15:22:26.282 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/search/popular-tags
2025-05-27 15:22:26.282  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /search
2025-05-27 15:22:26.282  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:26.282  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:26.283  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.283  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:26.283  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:26.283  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:22:26.283  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:26.283  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:26.284  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:26.284  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.284  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:26.284  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:26.284  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:26.284  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:26.284  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:26.284  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.284  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:26.285  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:26.285  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:26.285  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.285  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.285  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:26.285  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/search/popular-tags
2025-05-27 15:22:26.286  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:26.287 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:26.288  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:26.288 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:26.288 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:26.290 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:26.290  WARN 54336 --- [http-nio-8081-exec-4] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:26.291  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:26.291  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:26.291  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:26.291  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/search/popular-tags
2025-05-27 15:22:26.291 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /search/popular-tags?limit=10&_t=1748330546081] with attributes [permitAll]
2025-05-27 15:22:26.291 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /search/popular-tags?limit=10&_t=1748330546081
2025-05-27 15:22:26.292  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/search/popular-tags
2025-05-27 15:22:26.292  INFO 54336 --- [http-nio-8081-exec-4] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/search/popular-tags
2025-05-27 15:22:26.292 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.web.servlet.DispatcherServlet        : GET "/api/search/popular-tags?limit=10&_t=1748330546081", parameters={masked}
2025-05-27 15:22:26.292 DEBUG 54336 --- [http-nio-8081-exec-4] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.SearchController#getPopularTags(Integer)
2025-05-27 15:22:26.293 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.m.PhotoTagMapper.getPopularTags      : ==>  Preparing: SELECT tag_name as name, COUNT(photo_id) AS count FROM ptm_photo_tag GROUP BY tag_name ORDER BY count DESC LIMIT ?
2025-05-27 15:22:26.293 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.m.PhotoTagMapper.getPopularTags      : ==> Parameters: 10(Integer)
2025-05-27 15:22:26.294 DEBUG 54336 --- [http-nio-8081-exec-4] c.p.m.PhotoTagMapper.getPopularTags      : <==      Total: 6
2025-05-27 15:22:26.295 DEBUG 54336 --- [http-nio-8081-exec-4] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:26.296 DEBUG 54336 --- [http-nio-8081-exec-4] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=[TagDTO(name=PhotoTag成都, count=36), TagDTO(name=PhotoTag, c (truncated)...]
2025-05-27 15:22:26.297 DEBUG 54336 --- [http-nio-8081-exec-4] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:26.298 DEBUG 54336 --- [http-nio-8081-exec-4] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:26.453 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330546081
2025-05-27 15:22:26.453 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/interest-tags?limit=15&_t=1748330546081
2025-05-27 15:22:26.453 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /dict/data/code/photo_category?_t=1748330546081
2025-05-27 15:22:26.454 DEBUG 54336 --- [http-nio-8081-exec-8] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:26.454 DEBUG 54336 --- [http-nio-8081-exec-6] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:26.454  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:22:26.454 DEBUG 54336 --- [http-nio-8081-exec-7] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:26.454  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/dict/data/code/photo_category
2025-05-27 15:22:26.454 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/home
2025-05-27 15:22:26.454  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/interest-tags
2025-05-27 15:22:26.455 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/dict/data/code/photo_category
2025-05-27 15:22:26.455  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /dict
2025-05-27 15:22:26.455  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:22:26.455  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:26.455 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/interest-tags
2025-05-27 15:22:26.456  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:22:26.456  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:26.456  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:26.456  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.456  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:26.456  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:26.456  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:22:26.455  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:26.456  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:26.456  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:22:26.457  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.458  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:26.460  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:26.461  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/interest-tags
2025-05-27 15:22:26.462  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:26.462  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:26.462  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:26.462  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:26.462  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.462  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.462  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.462  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.462  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:26.462  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:26.463  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/dict/data/code/photo_category
2025-05-27 15:22:26.463  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/home
2025-05-27 15:22:26.464  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:26.464  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:26.464  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:26.465 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:26.465  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:26.465 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:26.465  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:26.466 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:26.466 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:26.466  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:26.466 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:26.466 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:26.466 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:26.467 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:26.467 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:26.467 DEBUG 54336 --- [http-nio-8081-exec-7] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:26.467 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:26.467 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:26.467  WARN 54336 --- [http-nio-8081-exec-7] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:26.468  WARN 54336 --- [http-nio-8081-exec-6] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:26.468  WARN 54336 --- [http-nio-8081-exec-8] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:26.468  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:26.468  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:26.468  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:26.468  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:26.468  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:26.468  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:26.468  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:26.469  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/interest-tags
2025-05-27 15:22:26.469  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:26.469  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:26.469 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/interest-tags?limit=15&_t=1748330546081] with attributes [permitAll]
2025-05-27 15:22:26.469  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/home
2025-05-27 15:22:26.469  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/dict/data/code/photo_category
2025-05-27 15:22:26.469 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/interest-tags?limit=15&_t=1748330546081
2025-05-27 15:22:26.470 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330546081] with attributes [permitAll]
2025-05-27 15:22:26.470  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/interest-tags
2025-05-27 15:22:26.470 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330546081
2025-05-27 15:22:26.470  INFO 54336 --- [http-nio-8081-exec-7] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/interest-tags
2025-05-27 15:22:26.470  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:22:26.470 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /dict/data/code/photo_category?_t=1748330546081] with attributes [permitAll]
2025-05-27 15:22:26.470  INFO 54336 --- [http-nio-8081-exec-6] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/home
2025-05-27 15:22:26.470 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/interest-tags?limit=15&_t=1748330546081", parameters={masked}
2025-05-27 15:22:26.470 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /dict/data/code/photo_category?_t=1748330546081
2025-05-27 15:22:26.470 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/home?page=1&size=20&category=recommend&_t=1748330546081", parameters={masked}
2025-05-27 15:22:26.471  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/dict/data/code/photo_category
2025-05-27 15:22:26.471 DEBUG 54336 --- [http-nio-8081-exec-7] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getUserInterestTags(int)
2025-05-27 15:22:26.471 DEBUG 54336 --- [http-nio-8081-exec-6] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getHomeRecommendations(int, int)
2025-05-27 15:22:26.471  INFO 54336 --- [http-nio-8081-exec-8] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/dict/data/code/photo_category
2025-05-27 15:22:26.471 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.web.servlet.DispatcherServlet        : GET "/api/dict/data/code/photo_category?_t=1748330546081", parameters={masked}
2025-05-27 15:22:26.471 DEBUG 54336 --- [http-nio-8081-exec-7] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:26.472  INFO 54336 --- [http-nio-8081-exec-6] c.p.s.impl.RecommendationServiceImpl     : 获取热门照片笔记，页码：1，每页大小：20
2025-05-27 15:22:26.472 DEBUG 54336 --- [http-nio-8081-exec-7] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=[风景, 美食, 旅行, 人像, 宠物, 城市, 自然, 建筑, 艺术, 生活])]
2025-05-27 15:22:26.472 DEBUG 54336 --- [http-nio-8081-exec-8] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.DictController#getDataByType(String)
2025-05-27 15:22:26.472 DEBUG 54336 --- [http-nio-8081-exec-7] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:26.472 DEBUG 54336 --- [http-nio-8081-exec-7] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:26.473 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.DictDataMapper.getByDictType  : ==>  Preparing: SELECT d.* FROM ptm_dict_data d JOIN ptm_dict_type t ON d.dict_type_id = t.id WHERE t.dict_type = ? AND d.status = 1 AND d.is_deleted = 0 AND t.is_deleted = 0 ORDER BY d.dict_sort
2025-05-27 15:22:26.473 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.DictDataMapper.getByDictType  : ==> Parameters: photo_category(String)
2025-05-27 15:22:26.474 DEBUG 54336 --- [http-nio-8081-exec-8] c.p.mapper.DictDataMapper.getByDictType  : <==      Total: 0
2025-05-27 15:22:26.475 DEBUG 54336 --- [http-nio-8081-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:26.475 DEBUG 54336 --- [http-nio-8081-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=[], timestamp=1748330546474)]
2025-05-27 15:22:26.476 DEBUG 54336 --- [http-nio-8081-exec-8] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:26.476 DEBUG 54336 --- [http-nio-8081-exec-8] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:26.478 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectHotPhotoNotes_mpCount      : ==>  Preparing: SELECT COUNT(*) AS total FROM ptm_photo_note pn WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
2025-05-27 15:22:26.478 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectHotPhotoNotes_mpCount      : ==> Parameters: 7(Integer)
2025-05-27 15:22:26.480 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectHotPhotoNotes_mpCount      : <==      Total: 1
2025-05-27 15:22:26.481 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectHotPhotoNotes              : ==>  Preparing: SELECT pn.id, pn.user_id, u.nickname, u.avatar, pn.title, pn.content, pn.photo_count, pn.view_count, pn.like_count, pn.comment_count, pn.share_count, pn.visibility, pn.allow_comment, pn.status, pn.reject_reason, pn.location, pn.longitude, pn.latitude, pn.created_at, pn.updated_at ,false as is_liked, false as is_collected FROM ptm_photo_note pn LEFT JOIN ptm_user u ON pn.user_id = u.id WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC LIMIT ?
2025-05-27 15:22:26.482 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectHotPhotoNotes              : ==> Parameters: 7(Integer), 20(Long)
2025-05-27 15:22:26.484 DEBUG 54336 --- [http-nio-8081-exec-6] c.p.m.P.selectHotPhotoNotes              : <==      Total: 3
2025-05-27 15:22:26.484 DEBUG 54336 --- [http-nio-8081-exec-6] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:26.485 DEBUG 54336 --- [http-nio-8081-exec-6] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@755c7 (truncated)...]
2025-05-27 15:22:26.486 DEBUG 54336 --- [http-nio-8081-exec-6] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:26.486 DEBUG 54336 --- [http-nio-8081-exec-6] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:26.545 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330546081
2025-05-27 15:22:26.545 DEBUG 54336 --- [http-nio-8081-exec-10] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:26.545  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:22:26.546 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/home
2025-05-27 15:22:26.546  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:22:26.546  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:26.546  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:26.546  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.546  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:26.546  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:26.546  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.547  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:26.548  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:26.548  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:26.548  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.548  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:26.548  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:26.548  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/home
2025-05-27 15:22:26.550  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:26.551 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:26.551  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:26.551 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:26.552 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:26.553 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:26.553  WARN 54336 --- [http-nio-8081-exec-10] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:26.553  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:26.554  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:26.554  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:26.554  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/home
2025-05-27 15:22:26.554 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330546081] with attributes [permitAll]
2025-05-27 15:22:26.554 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330546081
2025-05-27 15:22:26.554  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:22:26.555  INFO 54336 --- [http-nio-8081-exec-10] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/home
2025-05-27 15:22:26.555 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/home?page=1&size=20&category=recommend&_t=1748330546081", parameters={masked}
2025-05-27 15:22:26.555 DEBUG 54336 --- [http-nio-8081-exec-10] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getHomeRecommendations(int, int)
2025-05-27 15:22:26.556  INFO 54336 --- [http-nio-8081-exec-10] c.p.s.impl.RecommendationServiceImpl     : 获取热门照片笔记，页码：1，每页大小：20
2025-05-27 15:22:26.561 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.m.P.selectHotPhotoNotes_mpCount      : ==>  Preparing: SELECT COUNT(*) AS total FROM ptm_photo_note pn WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
2025-05-27 15:22:26.561 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.m.P.selectHotPhotoNotes_mpCount      : ==> Parameters: 7(Integer)
2025-05-27 15:22:26.562 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.m.P.selectHotPhotoNotes_mpCount      : <==      Total: 1
2025-05-27 15:22:26.563 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.m.P.selectHotPhotoNotes              : ==>  Preparing: SELECT pn.id, pn.user_id, u.nickname, u.avatar, pn.title, pn.content, pn.photo_count, pn.view_count, pn.like_count, pn.comment_count, pn.share_count, pn.visibility, pn.allow_comment, pn.status, pn.reject_reason, pn.location, pn.longitude, pn.latitude, pn.created_at, pn.updated_at ,false as is_liked, false as is_collected FROM ptm_photo_note pn LEFT JOIN ptm_user u ON pn.user_id = u.id WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC LIMIT ?
2025-05-27 15:22:26.564 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.m.P.selectHotPhotoNotes              : ==> Parameters: 7(Integer), 20(Long)
2025-05-27 15:22:26.573 DEBUG 54336 --- [http-nio-8081-exec-10] c.p.m.P.selectHotPhotoNotes              : <==      Total: 3
2025-05-27 15:22:26.573 DEBUG 54336 --- [http-nio-8081-exec-10] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:26.574 DEBUG 54336 --- [http-nio-8081-exec-10] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@306df (truncated)...]
2025-05-27 15:22:26.584 DEBUG 54336 --- [http-nio-8081-exec-10] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:26.584 DEBUG 54336 --- [http-nio-8081-exec-10] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:28.287 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /search/popular-tags?limit=10&_t=1748330548237
2025-05-27 15:22:28.287 DEBUG 54336 --- [http-nio-8081-exec-3] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:28.287  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/search/popular-tags
2025-05-27 15:22:28.287 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/search/popular-tags
2025-05-27 15:22:28.287  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /search
2025-05-27 15:22:28.288  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:28.288  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:28.288  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.289  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:28.289  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:28.289  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:22:28.289  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:28.289  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:28.289  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:28.289  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.290  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:28.290 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/interest-tags?limit=15&_t=1748330548237
2025-05-27 15:22:28.290 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330548237
2025-05-27 15:22:28.290  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:28.290 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /dict/data/code/photo_category?_t=1748330548237
2025-05-27 15:22:28.290 DEBUG 54336 --- [http-nio-8081-exec-9] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:28.290 DEBUG 54336 --- [http-nio-8081-exec-1] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:28.290  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:28.290 DEBUG 54336 --- [http-nio-8081-exec-5] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:28.290  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/dict/data/code/photo_category
2025-05-27 15:22:28.290  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/interest-tags
2025-05-27 15:22:28.290  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:22:28.290  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:28.290 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/dict/data/code/photo_category
2025-05-27 15:22:28.290 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/interest-tags
2025-05-27 15:22:28.290 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/home
2025-05-27 15:22:28.290  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:28.291  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /dict
2025-05-27 15:22:28.291  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:28.291  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:22:28.291  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:22:28.291  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.291  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:28.291  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:28.292  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:28.292  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:28.292  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.292  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:28.292  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:28.292  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:28.292  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:28.292  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.293  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.293  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:28.293  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:28.293  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:28.293  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:28.293  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.293  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:22:28.294  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.294  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:28.294  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:28.294  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:28.294  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:28.294  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:22:28.294  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:22:28.294  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:28.294  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/search/popular-tags
2025-05-27 15:22:28.295  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:28.295  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:28.295  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:28.295  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:28.295  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:28.296  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:28.295  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.296  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:28.296  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.296  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:28.296  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.296  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:28.296  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:28.297  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:28.297  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:28.297  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:28.297  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:28.297  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:28.297  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:28.297  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:28.297  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:28.297  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:28.298  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:28.298  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.298  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:28.298  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:28.298  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:28.298  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:28.299  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:28.299  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.299  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.300  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:28.299  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:28.299  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:28.300  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:28.300  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.300  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:28.300  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:28.300  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.300  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:28.300  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.301  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:28.301  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.301  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.301  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/dict/data/code/photo_category
2025-05-27 15:22:28.301  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.301 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:28.301  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:28.302  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:28.302  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:28.302  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/interest-tags
2025-05-27 15:22:28.302  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/home
2025-05-27 15:22:28.303 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:28.304 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:28.304  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:28.305  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:28.305  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:28.306 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:28.313  WARN 54336 --- [http-nio-8081-exec-3] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:28.314 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:28.314  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:28.314 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:28.314 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:28.314  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:28.314  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:28.314  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:28.314  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:28.314  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:28.315  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/search/popular-tags
2025-05-27 15:22:28.315 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:28.315 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:28.315 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:28.315 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /search/popular-tags?limit=10&_t=1748330548237] with attributes [permitAll]
2025-05-27 15:22:28.315 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:28.315 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:28.315 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:28.315 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /search/popular-tags?limit=10&_t=1748330548237
2025-05-27 15:22:28.316  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/search/popular-tags
2025-05-27 15:22:28.316  INFO 54336 --- [http-nio-8081-exec-3] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/search/popular-tags
2025-05-27 15:22:28.316 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : GET "/api/search/popular-tags?limit=10&_t=1748330548237", parameters={masked}
2025-05-27 15:22:28.317 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:28.317 DEBUG 54336 --- [http-nio-8081-exec-9] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:28.317 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:28.317 DEBUG 54336 --- [http-nio-8081-exec-3] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.SearchController#getPopularTags(Integer)
2025-05-27 15:22:28.317  WARN 54336 --- [http-nio-8081-exec-1] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:28.317  WARN 54336 --- [http-nio-8081-exec-9] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:28.317  WARN 54336 --- [http-nio-8081-exec-5] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:28.317  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:28.317  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:28.318  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:28.318 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.m.PhotoTagMapper.getPopularTags      : ==>  Preparing: SELECT tag_name as name, COUNT(photo_id) AS count FROM ptm_photo_tag GROUP BY tag_name ORDER BY count DESC LIMIT ?
2025-05-27 15:22:28.318  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:28.318  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:28.318  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:28.318  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:28.318 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.m.PhotoTagMapper.getPopularTags      : ==> Parameters: 10(Integer)
2025-05-27 15:22:28.318  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:28.318  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/home
2025-05-27 15:22:28.318  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:28.319  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/interest-tags
2025-05-27 15:22:28.319  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/dict/data/code/photo_category
2025-05-27 15:22:28.319 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330548237] with attributes [permitAll]
2025-05-27 15:22:28.319 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330548237
2025-05-27 15:22:28.319 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/interest-tags?limit=15&_t=1748330548237] with attributes [permitAll]
2025-05-27 15:22:28.319 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /dict/data/code/photo_category?_t=1748330548237] with attributes [permitAll]
2025-05-27 15:22:28.320  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:22:28.320 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/interest-tags?limit=15&_t=1748330548237
2025-05-27 15:22:28.320 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /dict/data/code/photo_category?_t=1748330548237
2025-05-27 15:22:28.320  INFO 54336 --- [http-nio-8081-exec-1] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/home
2025-05-27 15:22:28.320  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/interest-tags
2025-05-27 15:22:28.320  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/dict/data/code/photo_category
2025-05-27 15:22:28.320  INFO 54336 --- [http-nio-8081-exec-9] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/interest-tags
2025-05-27 15:22:28.320 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/home?page=1&size=20&category=recommend&_t=1748330548237", parameters={masked}
2025-05-27 15:22:28.320  INFO 54336 --- [http-nio-8081-exec-5] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/dict/data/code/photo_category
2025-05-27 15:22:28.321 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/interest-tags?limit=15&_t=1748330548237", parameters={masked}
2025-05-27 15:22:28.321 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.web.servlet.DispatcherServlet        : GET "/api/dict/data/code/photo_category?_t=1748330548237", parameters={masked}
2025-05-27 15:22:28.321 DEBUG 54336 --- [http-nio-8081-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getHomeRecommendations(int, int)
2025-05-27 15:22:28.321 DEBUG 54336 --- [http-nio-8081-exec-9] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getUserInterestTags(int)
2025-05-27 15:22:28.321 DEBUG 54336 --- [http-nio-8081-exec-3] c.p.m.PhotoTagMapper.getPopularTags      : <==      Total: 6
2025-05-27 15:22:28.322  INFO 54336 --- [http-nio-8081-exec-1] c.p.s.impl.RecommendationServiceImpl     : 获取热门照片笔记，页码：1，每页大小：20
2025-05-27 15:22:28.322 DEBUG 54336 --- [http-nio-8081-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:28.322 DEBUG 54336 --- [http-nio-8081-exec-9] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:28.322 DEBUG 54336 --- [http-nio-8081-exec-5] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.DictController#getDataByType(String)
2025-05-27 15:22:28.322 DEBUG 54336 --- [http-nio-8081-exec-9] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=[风景, 美食, 旅行, 人像, 宠物, 城市, 自然, 建筑, 艺术, 生活])]
2025-05-27 15:22:28.322 DEBUG 54336 --- [http-nio-8081-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=[TagDTO(name=PhotoTag成都, count=36), TagDTO(name=PhotoTag, c (truncated)...]
2025-05-27 15:22:28.323 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.DictDataMapper.getByDictType  : ==>  Preparing: SELECT d.* FROM ptm_dict_data d JOIN ptm_dict_type t ON d.dict_type_id = t.id WHERE t.dict_type = ? AND d.status = 1 AND d.is_deleted = 0 AND t.is_deleted = 0 ORDER BY d.dict_sort
2025-05-27 15:22:28.323 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.DictDataMapper.getByDictType  : ==> Parameters: photo_category(String)
2025-05-27 15:22:28.323 DEBUG 54336 --- [http-nio-8081-exec-9] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:28.324 DEBUG 54336 --- [http-nio-8081-exec-9] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:28.324 DEBUG 54336 --- [http-nio-8081-exec-3] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:28.324 DEBUG 54336 --- [http-nio-8081-exec-3] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:28.325 DEBUG 54336 --- [http-nio-8081-exec-5] c.p.mapper.DictDataMapper.getByDictType  : <==      Total: 0
2025-05-27 15:22:28.326 DEBUG 54336 --- [http-nio-8081-exec-5] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:28.326 DEBUG 54336 --- [http-nio-8081-exec-5] m.m.a.RequestResponseBodyMethodProcessor : Writing [ApiResponse(code=200, message=操作成功, data=[], timestamp=1748330548326)]
2025-05-27 15:22:28.328 DEBUG 54336 --- [http-nio-8081-exec-5] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:28.330 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.m.P.selectHotPhotoNotes_mpCount      : ==>  Preparing: SELECT COUNT(*) AS total FROM ptm_photo_note pn WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
2025-05-27 15:22:28.332 DEBUG 54336 --- [http-nio-8081-exec-5] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:28.333 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.m.P.selectHotPhotoNotes_mpCount      : ==> Parameters: 7(Integer)
2025-05-27 15:22:28.335 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.m.P.selectHotPhotoNotes_mpCount      : <==      Total: 1
2025-05-27 15:22:28.336 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.m.P.selectHotPhotoNotes              : ==>  Preparing: SELECT pn.id, pn.user_id, u.nickname, u.avatar, pn.title, pn.content, pn.photo_count, pn.view_count, pn.like_count, pn.comment_count, pn.share_count, pn.visibility, pn.allow_comment, pn.status, pn.reject_reason, pn.location, pn.longitude, pn.latitude, pn.created_at, pn.updated_at ,false as is_liked, false as is_collected FROM ptm_photo_note pn LEFT JOIN ptm_user u ON pn.user_id = u.id WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC LIMIT ?
2025-05-27 15:22:28.337 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.m.P.selectHotPhotoNotes              : ==> Parameters: 7(Integer), 20(Long)
2025-05-27 15:22:28.340 DEBUG 54336 --- [http-nio-8081-exec-1] c.p.m.P.selectHotPhotoNotes              : <==      Total: 3
2025-05-27 15:22:28.340 DEBUG 54336 --- [http-nio-8081-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:28.341 DEBUG 54336 --- [http-nio-8081-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@2fe3d (truncated)...]
2025-05-27 15:22:28.342 DEBUG 54336 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:28.343 DEBUG 54336 --- [http-nio-8081-exec-1] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:22:28.348 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330548237
2025-05-27 15:22:28.348 DEBUG 54336 --- [http-nio-8081-exec-2] s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-05-27 15:22:28.350  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:22:28.350 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 处理请求详情: GET /api/recommendation/home
2025-05-27 15:22:28.350  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求匹配白名单路径: /recommendation
2025-05-27 15:22:28.350  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 开始从请求中获取token
2025-05-27 15:22:28.350  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求头列表:
2025-05-27 15:22:28.351  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   cookie = JSESSIONID=42B22C61F51434F390FAA7849364A338; token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw; Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.351  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept-language = zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
2025-05-27 15:22:28.351  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept-encoding = gzip, deflate, br, zstd
2025-05-27 15:22:28.351  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   referer = http://localhost:3000/
2025-05-27 15:22:28.351  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-dest = empty
2025-05-27 15:22:28.351  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-mode = cors
2025-05-27 15:22:28.351  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-fetch-site = same-origin
2025-05-27 15:22:28.351  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   token = eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.352  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   dnt = 1
2025-05-27 15:22:28.352  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   accept = application/json, text/plain, */*
2025-05-27 15:22:28.352  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   user-agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-05-27 15:22:28.352  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-mobile = ?0
2025-05-27 15:22:28.352  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua = "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
2025-05-27 15:22:28.352  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   authorization = Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.353  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   sec-ch-ua-platform = "Windows"
2025-05-27 15:22:28.353  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   connection = close
2025-05-27 15:22:28.353  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     :   host = 127.0.0.1:8081
2025-05-27 15:22:28.353  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从请求头中获取Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.353  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从Authorization头成功提取token: eyJhbGciOiJIUzUxMiJ9.*******************************************************************************.x-uTY9dcoxtPyet3SPBIXD3Nc_w6HowKCaJR8nQTnCzWuYIT61nM7YQCXl5ErNgFJ26Gq44rwwxjysKNZC9_Rw
2025-05-27 15:22:28.353  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从请求中获取到JWT: 有效
2025-05-27 15:22:28.353  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 请求包含JWT，开始验证: /api/recommendation/home
2025-05-27 15:22:28.355  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtTokenProvider            : Token验证成功，用户: test, 过期时间: Wed May 28 11:26:06 CST 2025, 发行时间: Tue May 27 11:26:06 CST 2025
2025-05-27 15:22:28.357 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.security.JwtTokenProvider            : 从token中获取用户名: test
2025-05-27 15:22:28.357  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JWT有效，用户名: test
2025-05-27 15:22:28.358 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : ==>  Preparing: SELECT * FROM ptm_user WHERE username = ? AND is_deleted = 0 LIMIT 1
2025-05-27 15:22:28.359 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : ==> Parameters: test(String)
2025-05-27 15:22:28.361 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.mapper.UserMapper.selectByUsername   : <==      Total: 1
2025-05-27 15:22:28.363  WARN 54336 --- [http-nio-8081-exec-2] c.p.service.impl.EncryptionServiceImpl   : 解密字段 email 失败: Tag mismatch!
2025-05-27 15:22:28.364  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 从数据库查询用户: 成功
2025-05-27 15:22:28.364  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 成功设置认证信息到SecurityContext: test
2025-05-27 15:22:28.364  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 当前认证状态: 已认证, 用户: test, 权限: [ROLE_USER]
2025-05-27 15:22:28.364  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : 白名单请求，放行: /api/recommendation/home
2025-05-27 15:22:28.365 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor    : Authorized filter invocation [GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330548237] with attributes [permitAll]
2025-05-27 15:22:28.365 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /recommendation/home?page=1&size=20&category=recommend&_t=1748330548237
2025-05-27 15:22:28.365  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JwtAuthenticationFilter处理请求: GET /api/recommendation/home
2025-05-27 15:22:28.366  INFO 54336 --- [http-nio-8081-exec-2] c.p.security.JwtAuthenticationFilter     : JWT过滤器已应用，跳过: GET /api/recommendation/home
2025-05-27 15:22:28.366 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.web.servlet.DispatcherServlet        : GET "/api/recommendation/home?page=1&size=20&category=recommend&_t=1748330548237", parameters={masked}
2025-05-27 15:22:28.367 DEBUG 54336 --- [http-nio-8081-exec-2] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.phototagmoment.controller.RecommendationController#getHomeRecommendations(int, int)
2025-05-27 15:22:28.370  INFO 54336 --- [http-nio-8081-exec-2] c.p.s.impl.RecommendationServiceImpl     : 获取热门照片笔记，页码：1，每页大小：20
2025-05-27 15:22:28.378 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes_mpCount      : ==>  Preparing: SELECT COUNT(*) AS total FROM ptm_photo_note pn WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
2025-05-27 15:22:28.378 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes_mpCount      : ==> Parameters: 7(Integer)
2025-05-27 15:22:28.380 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes_mpCount      : <==      Total: 1
2025-05-27 15:22:28.381 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes              : ==>  Preparing: SELECT pn.id, pn.user_id, u.nickname, u.avatar, pn.title, pn.content, pn.photo_count, pn.view_count, pn.like_count, pn.comment_count, pn.share_count, pn.visibility, pn.allow_comment, pn.status, pn.reject_reason, pn.location, pn.longitude, pn.latitude, pn.created_at, pn.updated_at ,false as is_liked, false as is_collected FROM ptm_photo_note pn LEFT JOIN ptm_user u ON pn.user_id = u.id WHERE pn.status = 1 AND pn.is_deleted = 0 AND pn.visibility = 1 AND pn.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC LIMIT ?
2025-05-27 15:22:28.381 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes              : ==> Parameters: 7(Integer), 20(Long)
2025-05-27 15:22:28.384 DEBUG 54336 --- [http-nio-8081-exec-2] c.p.m.P.selectHotPhotoNotes              : <==      Total: 3
2025-05-27 15:22:28.385 DEBUG 54336 --- [http-nio-8081-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-05-27 15:22:28.385 DEBUG 54336 --- [http-nio-8081-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@3fb24 (truncated)...]
2025-05-27 15:22:28.387 DEBUG 54336 --- [http-nio-8081-exec-2] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-05-27 15:22:28.387 DEBUG 54336 --- [http-nio-8081-exec-2] s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-05-27 15:36:42.076  INFO 54336 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27 15:36:42.079  INFO 54336 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
