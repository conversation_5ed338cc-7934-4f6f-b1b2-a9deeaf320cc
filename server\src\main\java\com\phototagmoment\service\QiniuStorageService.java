package com.phototagmoment.service;

import com.phototagmoment.dto.UploadTokenDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 七牛云存储服务接口
 */
public interface QiniuStorageService {

    /**
     * 上传文件
     *
     * @param file     文件
     * @param fileName 文件名
     * @return 文件URL
     */
    String uploadFile(MultipartFile file, String fileName);

    /**
     * 上传文件
     *
     * @param inputStream 输入流
     * @param fileName    文件名
     * @param size        文件大小
     * @return 文件URL
     */
    String uploadFile(InputStream inputStream, String fileName, long size);

    /**
     * 删除文件
     *
     * @param fileUrl 文件URL
     * @return 是否成功
     */
    boolean deleteFile(String fileUrl);

    /**
     * 获取文件访问URL
     *
     * @param fileName 文件名
     * @return 文件访问URL
     */
    String getFileUrl(String fileName);

    /**
     * 获取上传凭证
     *
     * @param fileName 文件名
     * @return 上传凭证
     */
    String getUploadToken(String fileName);

    /**
     * 生成上传凭证信息
     *
     * @return 上传凭证信息
     */
    UploadTokenDTO generateUploadToken();

    /**
     * 获取文件
     *
     * @param fileUrl 文件URL
     * @return 文件输入流
     */
    InputStream getFile(String fileUrl);
}
